#!/bin/bash

# httpauth.c 最简化安装脚本
# 仅编译和安装HTTP认证插件，不包含其他配置

set -e

# 检查root权限
if [ "$EUID" -ne 0 ]; then
    echo "错误: 需要sudo权限运行此脚本"
    echo "用法: sudo ./install-minimal.sh"
    exit 1
fi

echo "=== httpauth.c 最简化安装 ==="
echo "日期: $(date)"
echo ""

# 检查源文件
if [ ! -f "httpauth.c" ]; then
    echo "错误: httpauth.c 文件不存在"
    exit 1
fi

echo "✓ 找到源文件: httpauth.c"

# 安装依赖
echo "安装编译依赖..."
apt-get update -qq
apt-get install -y ppp-dev libcurl4-openssl-dev libjson-c-dev libssl-dev build-essential pkg-config

echo "✓ 依赖安装完成"

# 获取pppd版本
PPPD_VERSION=$(pppd --version 2>&1 | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+' | head -1 || echo "2.4.9")
PLUGIN_PATH="/usr/lib/pppd/$PPPD_VERSION/httpauth.so"

echo "pppd版本: $PPPD_VERSION"
echo "安装路径: $PLUGIN_PATH"

# 编译插件
echo "编译插件..."
gcc -shared -fPIC -Wall -O2 -o httpauth.so httpauth.c \
    $(pkg-config --cflags --libs libcurl json-c openssl) \
    -I/usr/include/pppd \
    -DMAXOCTETS

if [ ! -f "httpauth.so" ]; then
    echo "错误: 编译失败"
    exit 1
fi

echo "✓ 编译成功"

# 安装插件
echo "安装插件..."
mkdir -p "/usr/lib/pppd/$PPPD_VERSION"

# 备份现有插件
if [ -f "$PLUGIN_PATH" ]; then
    cp "$PLUGIN_PATH" "${PLUGIN_PATH}.backup.$(date +%Y%m%d_%H%M%S)"
    echo "✓ 已备份现有插件"
fi

cp httpauth.so "$PLUGIN_PATH"
chmod 644 "$PLUGIN_PATH"

# 验证安装
if [ -f "$PLUGIN_PATH" ]; then
    echo "✓ 安装成功: $PLUGIN_PATH"
else
    echo "错误: 安装失败"
    exit 1
fi

# 清理
rm -f httpauth.so
echo "✓ 清理完成"

echo ""
echo "=== 安装完成 ==="
echo "插件路径: $PLUGIN_PATH"
echo "插件大小: $(ls -lh $PLUGIN_PATH | awk '{print $5}')"
echo ""
echo "使用方法:"
echo "在PPP配置文件中添加:"
echo "plugin $PLUGIN_PATH"
echo "auth-url https://your-server.com/auth"
echo "sign-key your-sign-key"
