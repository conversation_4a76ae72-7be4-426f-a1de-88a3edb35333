#!/bin/bash

# Cleanup unnecessary peer configurations for xl2tpd
# xl2tpd doesn't need individual peer files for L2TP connections

set -e

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_header "PPP Peer Configuration Cleanup"
echo "Analyzing peer configuration necessity for xl2tpd"
echo ""

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    print_error "This script needs to be run with sudo"
    echo "Usage: sudo ./cleanup-config.sh"
    exit 1
fi

# Analyze current peer configurations
print_header "1. Current Peer Configuration Analysis"

echo "Checking existing peer files..."

if [ -f "/etc/ppp/peers/httpauth-example" ]; then
    print_warning "Found example peer file: /etc/ppp/peers/httpauth-example"
    echo "   This is just an example and not needed for xl2tpd operation"
else
    print_success "No example peer file found"
fi

if [ -f "/etc/ppp/peers/httpauth" ]; then
    print_warning "Found peer file: /etc/ppp/peers/httpauth"
    echo "   This is not needed for xl2tpd L2TP server operation"
else
    print_success "No httpauth peer file found"
fi

# List all peer files
echo ""
echo "All peer files in /etc/ppp/peers/:"
if [ -d "/etc/ppp/peers" ]; then
    ls -la /etc/ppp/peers/ 2>/dev/null || echo "Directory is empty"
else
    echo "Peers directory doesn't exist"
fi

echo ""

# Explain xl2tpd operation
print_header "2. xl2tpd Operation Explanation"

echo ""
echo "📋 How xl2tpd works with PPP:"
echo ""
echo "1. **xl2tpd listens for L2TP connections** on port 1701"
echo "2. **Automatically creates PPP sessions** for each L2TP tunnel"
echo "3. **Uses global PPP options** from /etc/ppp/options.xl2tpd"
echo "4. **No individual peer files needed** for L2TP clients"
echo ""
echo "🔧 Required configuration files:"
echo "   ✓ /etc/xl2tpd/xl2tpd.conf - xl2tpd server configuration"
echo "   ✓ /etc/ppp/options.xl2tpd - PPP options for all L2TP connections"
echo "   ✓ /etc/xl2tpd/l2tp-secrets - Required by xl2tpd (can be minimal)"
echo ""
echo "❌ NOT needed for xl2tpd:"
echo "   ✗ /etc/ppp/peers/httpauth - Individual peer files"
echo "   ✗ /etc/ppp/peers/httpauth-example - Example files"
echo ""

# Recommendations
print_header "3. Configuration Recommendations"

echo ""
echo "🎯 **Recommended action**: Remove unnecessary peer files"
echo ""
echo "Peer files are useful for:"
echo "   - Direct PPP dial-up connections"
echo "   - PPPoE connections"
echo "   - Manual PPP sessions"
echo ""
echo "Peer files are NOT needed for:"
echo "   - xl2tpd L2TP server operation"
echo "   - Automatic L2TP client connections"
echo "   - HTTP authentication plugin"
echo ""

# Offer cleanup options
print_header "4. Cleanup Options"

echo ""
echo "Choose an action:"
echo "1. Remove example peer files (recommended)"
echo "2. Keep example files for reference"
echo "3. Show current configuration and exit"
echo ""

read -p "Enter your choice (1-3): " choice

case $choice in
    1)
        print_header "Removing Example Peer Files"
        
        if [ -f "/etc/ppp/peers/httpauth-example" ]; then
            rm -f /etc/ppp/peers/httpauth-example
            print_success "Removed /etc/ppp/peers/httpauth-example"
        fi
        
        if [ -f "/etc/ppp/peers/httpauth" ]; then
            echo "Found /etc/ppp/peers/httpauth"
            read -p "Remove this file too? (y/n): " remove_httpauth
            if [ "$remove_httpauth" = "y" ] || [ "$remove_httpauth" = "Y" ]; then
                rm -f /etc/ppp/peers/httpauth
                print_success "Removed /etc/ppp/peers/httpauth"
            fi
        fi
        
        print_success "Cleanup completed"
        ;;
        
    2)
        print_warning "Keeping example files for reference"
        echo "Note: These files are not used by xl2tpd but can serve as documentation"
        ;;
        
    3)
        print_header "Current Configuration Summary"
        
        echo ""
        echo "xl2tpd configuration:"
        if [ -f "/etc/xl2tpd/xl2tpd.conf" ]; then
            echo "✓ /etc/xl2tpd/xl2tpd.conf exists"
        else
            echo "✗ /etc/xl2tpd/xl2tpd.conf missing"
        fi
        
        echo ""
        echo "PPP configuration:"
        if [ -f "/etc/ppp/options.xl2tpd" ]; then
            echo "✓ /etc/ppp/options.xl2tpd exists"
        else
            echo "✗ /etc/ppp/options.xl2tpd missing"
        fi
        
        echo ""
        echo "Peer files:"
        if [ -d "/etc/ppp/peers" ]; then
            ls -la /etc/ppp/peers/ 2>/dev/null || echo "No peer files"
        fi
        
        exit 0
        ;;
        
    *)
        print_error "Invalid choice"
        exit 1
        ;;
esac

# Final verification
print_header "5. Final Configuration Status"

echo ""
echo "📋 Current essential configuration:"
echo ""

if [ -f "/etc/xl2tpd/xl2tpd.conf" ]; then
    print_success "xl2tpd server config: /etc/xl2tpd/xl2tpd.conf"
else
    print_error "Missing: /etc/xl2tpd/xl2tpd.conf"
fi

if [ -f "/etc/ppp/options.xl2tpd" ]; then
    print_success "PPP options: /etc/ppp/options.xl2tpd"
else
    print_error "Missing: /etc/ppp/options.xl2tpd"
fi

if [ -f "/etc/xl2tpd/l2tp-secrets" ]; then
    print_success "L2TP secrets: /etc/xl2tpd/l2tp-secrets"
else
    print_warning "Missing: /etc/xl2tpd/l2tp-secrets (will be created automatically)"
fi

echo ""
echo "🚀 To test xl2tpd:"
echo "1. sudo systemctl start xl2tpd"
echo "2. sudo systemctl status xl2tpd"
echo "3. test_auth test001 password"
echo "4. Connect L2TP client to server"

echo ""
print_success "Configuration analysis completed!"
echo ""
echo "Remember: xl2tpd automatically handles L2TP connections"
echo "No individual peer files are needed for normal operation."
