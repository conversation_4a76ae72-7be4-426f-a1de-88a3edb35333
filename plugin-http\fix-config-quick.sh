#!/bin/bash

# Quick configuration fix for xl2tpd and PPP
# Removes problematic options as requested

set -e

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    print_error "This script needs to be run with sudo"
    echo "Usage: sudo ./fix-config-quick.sh"
    exit 1
fi

print_header "Quick Configuration Fix"
echo "Removing problematic xl2tpd and PPP options"
echo ""

# Step 1: Stop xl2tpd service
print_header "Step 1: Stopping xl2tpd Service"
systemctl stop xl2tpd 2>/dev/null || true
print_success "xl2tpd service stopped"

# Step 2: Fix xl2tpd configuration
print_header "Step 2: Fixing xl2tpd Configuration"

# Backup existing configuration
if [ -f "/etc/xl2tpd/xl2tpd.conf" ]; then
    cp /etc/xl2tpd/xl2tpd.conf /etc/xl2tpd/xl2tpd.conf.backup.$(date +%Y%m%d_%H%M%S)
    print_success "Configuration backed up"
fi

# Create corrected xl2tpd configuration (without problematic options)
cat > /etc/xl2tpd/xl2tpd.conf << 'EOF'
; xl2tpd configuration for Ubuntu 24.04
; Minimal configuration without problematic options

[global]
listen-addr = 0.0.0.0
port = 1701
auth file = /etc/xl2tpd/l2tp-secrets

[lns default]
name = HTTPAuthVPNServer
ip range = ***********-***********0
local ip = **********
require chap = no
refuse pap = no
require authentication = yes
pppoptfile = /etc/ppp/options.xl2tpd
length bit = yes
challenge = no
exclusive = no
EOF

print_success "Fixed xl2tpd configuration (removed: max sessions, hello, flow control)"

# Step 3: Fix PPP configuration
print_header "Step 3: Fixing PPP Configuration"

# Get plugin path
PPPD_VERSION=$(pppd --version 2>&1 | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+' | head -1 || echo "2.4.9")
PLUGIN_PATH="/usr/lib/pppd/$PPPD_VERSION/httpauth.so"

# Backup existing PPP configuration
if [ -f "/etc/ppp/options.xl2tpd" ]; then
    cp /etc/ppp/options.xl2tpd /etc/ppp/options.xl2tpd.backup.$(date +%Y%m%d_%H%M%S)
    print_success "PPP configuration backed up"
fi

# Create corrected PPP configuration (without lock option)
cat > /etc/ppp/options.xl2tpd << EOF
# PPP options for xl2tpd with HTTP authentication
# Fixed configuration without problematic options

# Authentication settings
require-pap
refuse-chap
refuse-mschap
refuse-mschap-v2
refuse-eap

# HTTP Authentication Plugin
plugin $PLUGIN_PATH
auth-url https://testapi.softapi.cn/notify/pcm_ok
sign-key hYC0ztcOKp2aZ5t0

# IP authorization settings (fixes "Peer is not authorized" error)
noauth
ipcp-accept-local
ipcp-accept-remote

# Network settings (removed 'lock' option)
proxyarp
nobsdcomp
nodeflate
noipdefault

# DNS settings
ms-dns *******
ms-dns *******

# Connection settings
lcp-echo-interval 30
lcp-echo-failure 4
idle 1800

# Logging
logfile /var/log/xl2tpd.log
EOF

print_success "Fixed PPP configuration (removed: lock)"

# Step 4: Ensure l2tp-secrets file exists
print_header "Step 4: Checking L2TP Secrets File"

if [ ! -f "/etc/xl2tpd/l2tp-secrets" ]; then
    cat > /etc/xl2tpd/l2tp-secrets << 'EOF'
# L2TP secrets file for xl2tpd
# This file is required but authentication is handled by HTTP plugin
* * * *
EOF
    chmod 600 /etc/xl2tpd/l2tp-secrets
    print_success "Created l2tp-secrets file"
else
    print_success "l2tp-secrets file already exists"
fi

# Step 5: Start xl2tpd service
print_header "Step 5: Starting xl2tpd Service"

systemctl start xl2tpd

# Wait a moment for service to start
sleep 3

if systemctl is-active xl2tpd >/dev/null 2>&1; then
    print_success "xl2tpd service started successfully"
else
    print_error "xl2tpd service failed to start"
    echo "Checking logs..."
    journalctl -u xl2tpd --no-pager -n 5
    exit 1
fi

# Step 6: Verify service status
print_header "Step 6: Service Verification"

echo "Service status:"
systemctl status xl2tpd --no-pager -l | head -10

echo ""
echo "Port listening check:"
if netstat -ulnp 2>/dev/null | grep ":1701"; then
    print_success "xl2tpd is listening on port 1701"
    netstat -ulnp | grep ":1701"
else
    print_warning "xl2tpd may not be listening on port 1701"
fi

# Step 7: Display configuration summary
print_header "Configuration Fix Summary"

echo ""
echo "🔧 Changes made:"
echo "   ✓ Removed from xl2tpd.conf:"
echo "     - max sessions = 50"
echo "     - hello = 60"
echo "     - flow control = no"
echo ""
echo "   ✓ Removed from PPP options:"
echo "     - lock"
echo ""
echo "📋 Current configuration:"
echo "   xl2tpd config: /etc/xl2tpd/xl2tpd.conf"
echo "   PPP options: /etc/ppp/options.xl2tpd"
echo "   Server IP: **********"
echo "   Client IP range: ***********-***********0"
echo ""

print_header "Testing Instructions"

echo ""
echo "🧪 To test the configuration:"
echo ""
echo "1. Check service status:"
echo "   sudo systemctl status xl2tpd"
echo ""
echo "2. Test authentication:"
echo "   test_auth test001 password"
echo ""
echo "3. Monitor logs:"
echo "   sudo tail -f /var/log/xl2tpd.log"
echo ""
echo "4. Try L2TP connection from client"
echo ""

if systemctl is-active xl2tpd >/dev/null 2>&1; then
    print_success "Configuration fix completed successfully!"
    echo ""
    echo "xl2tpd is now running with the corrected configuration."
    echo "The problematic options have been removed."
else
    print_error "Service is still not running properly."
fi

echo ""
echo "Configuration files have been updated according to your requirements."
