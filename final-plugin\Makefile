# 简洁的Makefile - 专门用于编译httpauth.c
# HTTP认证插件编译配置

# 编译器设置
CC = gcc
CFLAGS = -shared -fPIC -Wall -O2
INCLUDES = -I/usr/include/pppd

# 库依赖
LIBS = $(shell pkg-config --cflags --libs libcurl json-c openssl)

# 目标文件
PLUGIN = httpauth.so
SOURCE = httpauth.c

# pppd版本检测
PPPD_VERSION = $(shell pppd --version 2>&1 | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+' | head -1 || echo "2.4.9")
PLUGIN_DIR = /usr/lib/pppd/$(PPPD_VERSION)

# 默认目标
all: $(PLUGIN)

# 编译插件
$(PLUGIN): $(SOURCE)
	@echo "编译HTTP认证插件..."
	@echo "pppd版本: $(PPPD_VERSION)"
	@echo "目标目录: $(PLUGIN_DIR)"
	$(CC) $(CFLAGS) $(INCLUDES) -o $(PLUGIN) $(SOURCE) $(LIBS)
	@echo "编译完成: $(PLUGIN)"

# 安装插件
install: $(PLUGIN)
	@echo "安装插件到: $(PLUGIN_DIR)/$(PLUGIN)"
	@mkdir -p $(PLUGIN_DIR)
	@if [ -f "$(PLUGIN_DIR)/$(PLUGIN)" ]; then \
		cp "$(PLUGIN_DIR)/$(PLUGIN)" "$(PLUGIN_DIR)/$(PLUGIN).backup.$$(date +%Y%m%d_%H%M%S)"; \
		echo "已备份现有插件"; \
	fi
	cp $(PLUGIN) $(PLUGIN_DIR)/
	chmod 644 $(PLUGIN_DIR)/$(PLUGIN)
	@echo "安装完成"

# 卸载插件
uninstall:
	@echo "卸载插件: $(PLUGIN_DIR)/$(PLUGIN)"
	@if [ -f "$(PLUGIN_DIR)/$(PLUGIN)" ]; then \
		rm -f "$(PLUGIN_DIR)/$(PLUGIN)"; \
		echo "插件已卸载"; \
	else \
		echo "插件未安装"; \
	fi

# 清理编译文件
clean:
	@echo "清理编译文件..."
	rm -f $(PLUGIN)
	@echo "清理完成"

# 检查依赖
check-deps:
	@echo "检查编译依赖..."
	@which gcc > /dev/null || (echo "错误: gcc未安装" && exit 1)
	@which pkg-config > /dev/null || (echo "错误: pkg-config未安装" && exit 1)
	@pkg-config --exists libcurl || (echo "错误: libcurl-dev未安装" && exit 1)
	@pkg-config --exists json-c || (echo "错误: libjson-c-dev未安装" && exit 1)
	@pkg-config --exists openssl || (echo "错误: libssl-dev未安装" && exit 1)
	@test -d /usr/include/pppd || (echo "错误: ppp-dev未安装" && exit 1)
	@echo "所有依赖检查通过"

# 显示信息
info:
	@echo "HTTP认证插件编译信息:"
	@echo "  源文件: $(SOURCE)"
	@echo "  目标文件: $(PLUGIN)"
	@echo "  pppd版本: $(PPPD_VERSION)"
	@echo "  安装目录: $(PLUGIN_DIR)"
	@echo "  编译器: $(CC)"
	@echo "  编译选项: $(CFLAGS)"
	@echo "  包含路径: $(INCLUDES)"
	@echo "  链接库: $(LIBS)"

# 测试编译
test: $(PLUGIN)
	@echo "测试插件文件..."
	@file $(PLUGIN)
	@echo "检查依赖库..."
	@ldd $(PLUGIN) | grep -E "(curl|json|ssl|crypto)" || echo "核心依赖库已链接"
	@echo "插件大小: $$(ls -lh $(PLUGIN) | awk '{print $$5}')"

# 帮助信息
help:
	@echo "HTTP认证插件编译Makefile"
	@echo ""
	@echo "可用目标:"
	@echo "  all        - 编译插件 (默认)"
	@echo "  install    - 编译并安装插件"
	@echo "  uninstall  - 卸载插件"
	@echo "  clean      - 清理编译文件"
	@echo "  check-deps - 检查编译依赖"
	@echo "  info       - 显示编译信息"
	@echo "  test       - 测试编译结果"
	@echo "  help       - 显示此帮助信息"
	@echo ""
	@echo "使用示例:"
	@echo "  make              # 编译插件"
	@echo "  sudo make install # 安装插件"
	@echo "  make clean        # 清理文件"

.PHONY: all install uninstall clean check-deps info test help
