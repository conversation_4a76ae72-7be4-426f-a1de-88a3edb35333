#!/bin/bash

# Verify HTTP Authentication Plugin Installation on Ubuntu 24.04
# Copyright 2024

set -e

echo "=== HTTP Authentication Plugin Installation Verification ==="
echo "System: $(lsb_release -d 2>/dev/null | cut -f2 || echo 'Ubuntu 24.04')"
echo "Date: $(date)"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✓${NC} $2"
    else
        echo -e "${RED}✗${NC} $2"
    fi
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

# Check if running as root for some tests
if [ "$EUID" -eq 0 ]; then
    SUDO=""
else
    SUDO="sudo"
fi

echo "1. Checking Dependencies"
echo "========================"

# Check pppd
if command -v pppd >/dev/null 2>&1; then
    PPPD_VERSION=$(pppd --version 2>&1 | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+' | head -1 || echo "unknown")
    print_status 0 "pppd installed: version $PPPD_VERSION"
else
    print_status 1 "pppd not found"
fi

# Check pppd headers
if [ -d "/usr/include/pppd" ]; then
    print_status 0 "pppd headers: /usr/include/pppd"
else
    print_status 1 "pppd headers not found (install ppp-dev)"
fi

# Check libraries
for lib in libcurl json-c openssl; do
    if pkg-config --exists $lib; then
        VERSION=$(pkg-config --modversion $lib)
        print_status 0 "$lib: version $VERSION"
    else
        print_status 1 "$lib not found"
    fi
done

echo ""
echo "2. Checking Build Files"
echo "======================="

# Check source files
for file in httpauth.c httpauth.h Makefile; do
    if [ -f "$file" ]; then
        print_status 0 "Source file: $file"
    else
        print_status 1 "Missing source file: $file"
    fi
done

# Check if plugin is built
if [ -f "httpauth.so" ]; then
    print_status 0 "Plugin built: httpauth.so"
    echo "   Size: $(ls -lh httpauth.so | awk '{print $5}')"
    echo "   Dependencies:"
    ldd httpauth.so | sed 's/^/     /'
else
    print_status 1 "Plugin not built (run ./build.sh)"
fi

# Check test program
if [ -f "test_auth" ]; then
    print_status 0 "Test program built: test_auth"
else
    print_status 1 "Test program not built"
fi

echo ""
echo "3. Checking Installation"
echo "======================="

# Determine pppd version and plugin directory
if command -v pppd >/dev/null 2>&1; then
    PPPD_VERSION=$(pppd --version 2>&1 | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+' | head -1 || echo "2.4.9")
    PLUGIN_DIR="/usr/lib/pppd/$PPPD_VERSION"
    
    if [ -f "$PLUGIN_DIR/httpauth.so" ]; then
        print_status 0 "Plugin installed: $PLUGIN_DIR/httpauth.so"
        echo "   Size: $(ls -lh $PLUGIN_DIR/httpauth.so | awk '{print $5}')"
        echo "   Permissions: $(ls -l $PLUGIN_DIR/httpauth.so | awk '{print $1}')"
    else
        print_status 1 "Plugin not installed (run sudo make install)"
        print_warning "Expected location: $PLUGIN_DIR/httpauth.so"
    fi
else
    print_status 1 "Cannot determine plugin directory (pppd not found)"
fi

# Check man page
if [ -f "/usr/share/man/man8/httpauth.8" ]; then
    print_status 0 "Man page installed: /usr/share/man/man8/httpauth.8"
else
    print_status 1 "Man page not installed"
fi

echo ""
echo "4. Testing Plugin Loading"
echo "========================"

if [ -f "$PLUGIN_DIR/httpauth.so" ]; then
    echo "Testing plugin loading with pppd..."
    if timeout 5 $SUDO pppd plugin $PLUGIN_DIR/httpauth.so nodetach 2>&1 | grep -q "HTTP authentication plugin initialized"; then
        print_status 0 "Plugin loads successfully"
    else
        print_status 1 "Plugin loading failed"
        print_warning "Try: sudo pppd plugin $PLUGIN_DIR/httpauth.so debug"
    fi
else
    print_warning "Skipping plugin loading test (plugin not installed)"
fi

echo ""
echo "5. Testing HTTP Authentication"
echo "============================="

if [ -f "test_auth" ]; then
    echo "Testing authentication API..."
    if ./test_auth testuser testpass >/dev/null 2>&1; then
        print_status 0 "HTTP authentication test passed"
    else
        print_status 1 "HTTP authentication test failed"
        print_warning "Check network connectivity and API endpoint"
    fi
else
    print_warning "Skipping authentication test (test program not built)"
fi

echo ""
echo "6. Configuration Check"
echo "====================="

# Check PPP directories
if [ -d "/etc/ppp" ]; then
    print_status 0 "PPP config directory: /etc/ppp"
    echo "   Contents:"
    ls -la /etc/ppp/ | sed 's/^/     /'
else
    print_status 1 "PPP config directory not found"
fi

# Check for existing configuration
if grep -q "httpauth" /etc/ppp/options 2>/dev/null; then
    print_status 0 "Plugin configured in /etc/ppp/options"
else
    print_warning "Plugin not configured in /etc/ppp/options"
fi

echo ""
echo "=== Verification Summary ==="

# Count issues
ISSUES=0

# Essential checks
[ ! -f "httpauth.so" ] && ISSUES=$((ISSUES + 1))
[ ! -f "$PLUGIN_DIR/httpauth.so" ] && ISSUES=$((ISSUES + 1))
[ ! -d "/usr/include/pppd" ] && ISSUES=$((ISSUES + 1))
! pkg-config --exists libcurl && ISSUES=$((ISSUES + 1))
! pkg-config --exists json-c && ISSUES=$((ISSUES + 1))
! pkg-config --exists openssl && ISSUES=$((ISSUES + 1))

if [ $ISSUES -eq 0 ]; then
    echo -e "${GREEN}✓ Installation appears to be complete and functional${NC}"
    echo ""
    echo "Next steps:"
    echo "1. Configure PPP: edit /etc/ppp/options or /etc/ppp/peers/[peer-name]"
    echo "2. Add plugin line: plugin $PLUGIN_DIR/httpauth.so"
    echo "3. Set auth-url and sign-key options"
    echo "4. Test with: sudo pppd call [peer-name]"
else
    echo -e "${RED}✗ Found $ISSUES issue(s) that need attention${NC}"
    echo ""
    echo "Recommended actions:"
    [ ! -f "httpauth.so" ] && echo "- Run: ./build.sh"
    [ ! -f "$PLUGIN_DIR/httpauth.so" ] && echo "- Run: sudo make install"
    [ ! -d "/usr/include/pppd" ] && echo "- Run: sudo apt-get install ppp-dev"
    ! pkg-config --exists libcurl && echo "- Run: sudo apt-get install libcurl4-openssl-dev"
    ! pkg-config --exists json-c && echo "- Run: sudo apt-get install libjson-c-dev"
    ! pkg-config --exists openssl && echo "- Run: sudo apt-get install libssl-dev"
fi

echo ""
echo "For detailed setup instructions, see: QUICKSTART-Ubuntu24.04.md"
