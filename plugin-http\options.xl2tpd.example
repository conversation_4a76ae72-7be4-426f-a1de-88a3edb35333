# PPP options for xl2tpd with HTTP authentication
# /etc/ppp/options.xl2tpd
#
# This file contains PPP options that will be used by xl2tpd
# when establishing L2TP connections with HTTP authentication

# ============================================================================
# Authentication Settings
# ============================================================================

# Require PAP authentication (compatible with HTTP auth plugin)
require-pap

# Refuse other authentication methods
refuse-chap
refuse-mschap
refuse-mschap-v2
refuse-eap

# ============================================================================
# HTTP Authentication Plugin
# ============================================================================

# Load HTTP authentication plugin
plugin /usr/lib/pppd/2.4.9/httpauth.so

# Authentication server URL
auth-url https://testapi.softapi.cn/notify/pcm_ok

# Signature key for verification
sign-key hYC0ztcOKp2aZ5t0

# ============================================================================
# Network Settings
# ============================================================================

# Enable proxy ARP (allows clients to communicate with LAN)
proxyarp

# Don't set default route on server
nodefaultroute

# Disable BSD compression
nobsdcomp

# Disable Deflate compression
nodeflate

# Don't assign IP to the server interface
noipdefault

# ============================================================================
# DNS Settings
# ============================================================================

# Primary DNS server
ms-dns *******

# Secondary DNS server
ms-dns *******

# Alternative DNS servers (uncomment if needed)
# ms-dns *******
# ms-dns *******

# ============================================================================
# Connection Settings
# ============================================================================

# LCP echo settings for keepalive
lcp-echo-interval 30
lcp-echo-failure 4

# Idle timeout (seconds) - disconnect after 30 minutes of inactivity
idle 1800

# Maximum transmission unit
mtu 1400
mru 1400

# ============================================================================
# Logging Settings
# ============================================================================

# Log file location
logfile /var/log/xl2tpd.log

# Uncomment for debug logging
# debug

# ============================================================================
# Security Settings
# ============================================================================

# Hide password in logs
hide-password

# Require authentication
auth

# Don't allow unauthenticated connections
# noauth

# ============================================================================
# Advanced Settings (uncomment if needed)
# ============================================================================

# Async character map
# asyncmap 0

# Use hardware flow control
# crtscts

# Don't use modem control lines
# local

# Set connect script timeout
# connect-delay 5000

# Maximum connect time (seconds)
# maxconnect 0

# Persist connection
# persist

# Maximum number of failed attempts
# maxfail 0

# ============================================================================
# IP Pool Settings (if using static IP assignment)
# ============================================================================

# Uncomment and modify if you want to assign specific IP ranges
# This is typically handled by xl2tpd configuration instead

# Example: Assign IPs from a specific range
# *************:*************

# ============================================================================
# MPPE Encryption (uncomment if needed)
# ============================================================================

# Require MPPE encryption
# require-mppe

# Allow MPPE 40-bit encryption
# require-mppe-40

# Allow MPPE 128-bit encryption
# require-mppe-128

# ============================================================================
# Routing Settings
# ============================================================================

# Don't replace default route
# nodefaultroute

# Use peer DNS
# usepeerdns

# Replace default route
# replacedefaultroute

# ============================================================================
# Example Configurations for Different Scenarios
# ============================================================================

# For high-security environments:
# require-mppe-128
# refuse-pap
# require-chap

# For maximum compatibility:
# noauth
# nodefaultroute

# For debugging:
# debug
# kdebug 1
# logfile /var/log/ppp-debug.log

# ============================================================================
# Notes
# ============================================================================

# 1. This configuration is optimized for xl2tpd with HTTP authentication
# 2. Adjust IP ranges and DNS servers according to your network
# 3. The HTTP authentication plugin handles user verification
# 4. Monitor /var/log/xl2tpd.log for connection issues
# 5. Test configuration with: pppd file /etc/ppp/options.xl2tpd nodetach
# 6. For production, remove debug options and set appropriate timeouts
