#!/bin/bash

# Configuration verification script
# Checks that problematic options have been removed

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_header "Configuration Verification"
echo "Checking for problematic options that should be removed"
echo ""

# Check xl2tpd configuration
print_header "1. Checking xl2tpd Configuration"

if [ -f "/etc/xl2tpd/xl2tpd.conf" ]; then
    print_success "xl2tpd.conf exists"
    
    # Check for problematic options
    ISSUES=0
    
    if grep -q "max sessions" /etc/xl2tpd/xl2tpd.conf; then
        print_error "Found 'max sessions' option (should be removed)"
        ISSUES=$((ISSUES + 1))
    else
        print_success "No 'max sessions' option found"
    fi
    
    if grep -q "hello" /etc/xl2tpd/xl2tpd.conf; then
        print_error "Found 'hello' option (should be removed)"
        ISSUES=$((ISSUES + 1))
    else
        print_success "No 'hello' option found"
    fi
    
    if grep -q "flow control" /etc/xl2tpd/xl2tpd.conf; then
        print_error "Found 'flow control' option (should be removed)"
        ISSUES=$((ISSUES + 1))
    else
        print_success "No 'flow control' option found"
    fi
    
    if grep -q "tunnel authentication" /etc/xl2tpd/xl2tpd.conf; then
        print_error "Found 'tunnel authentication' option (should be removed)"
        ISSUES=$((ISSUES + 1))
    else
        print_success "No 'tunnel authentication' option found"
    fi
    
    echo ""
    echo "Current xl2tpd.conf content:"
    echo "=========================="
    cat /etc/xl2tpd/xl2tpd.conf
    echo ""
    
else
    print_error "xl2tpd.conf not found"
    ISSUES=$((ISSUES + 1))
fi

# Check PPP configuration
print_header "2. Checking PPP Configuration"

if [ -f "/etc/ppp/options.xl2tpd" ]; then
    print_success "options.xl2tpd exists"
    
    if grep -q "^lock" /etc/ppp/options.xl2tpd; then
        print_error "Found 'lock' option (should be removed)"
        ISSUES=$((ISSUES + 1))
    else
        print_success "No 'lock' option found"
    fi
    
    # Check for required options
    if grep -q "noauth" /etc/ppp/options.xl2tpd; then
        print_success "Found 'noauth' option (required for IP authorization)"
    else
        print_warning "Missing 'noauth' option (may cause IP authorization issues)"
    fi
    
    if grep -q "ipcp-accept" /etc/ppp/options.xl2tpd; then
        print_success "Found IPCP accept options"
    else
        print_warning "Missing IPCP accept options"
    fi
    
    echo ""
    echo "Current options.xl2tpd content:"
    echo "=============================="
    cat /etc/ppp/options.xl2tpd
    echo ""
    
else
    print_error "options.xl2tpd not found"
    ISSUES=$((ISSUES + 1))
fi

# Check service status
print_header "3. Checking Service Status"

if systemctl is-active xl2tpd >/dev/null 2>&1; then
    print_success "xl2tpd service is running"
else
    print_warning "xl2tpd service is not running"
    echo "Start with: sudo systemctl start xl2tpd"
fi

if netstat -ulnp 2>/dev/null | grep -q ":1701"; then
    print_success "xl2tpd is listening on port 1701"
else
    print_warning "xl2tpd may not be listening on port 1701"
fi

# Summary
print_header "4. Verification Summary"

echo ""
if [ $ISSUES -eq 0 ]; then
    print_success "All problematic options have been removed"
    echo ""
    echo "✅ Configuration is clean:"
    echo "   - No 'max sessions' in xl2tpd.conf"
    echo "   - No 'hello' in xl2tpd.conf"
    echo "   - No 'flow control' in xl2tpd.conf"
    echo "   - No 'tunnel authentication' in xl2tpd.conf"
    echo "   - No 'lock' in options.xl2tpd"
else
    print_error "Found $ISSUES configuration issue(s)"
    echo ""
    echo "🔧 To fix these issues, run:"
    echo "   sudo ./fix-config-quick.sh"
fi

echo ""
echo "📋 Current configuration summary:"
echo "   xl2tpd config: /etc/xl2tpd/xl2tpd.conf"
echo "   PPP options: /etc/ppp/options.xl2tpd"
echo "   Service status: $(systemctl is-active xl2tpd 2>/dev/null || echo 'inactive')"

echo ""
print_header "5. Test Commands"

echo ""
echo "🧪 To test the configuration:"
echo ""
echo "1. Test authentication:"
echo "   test_auth test001 password"
echo ""
echo "2. Check service logs:"
echo "   sudo journalctl -u xl2tpd -f"
echo ""
echo "3. Monitor PPP logs:"
echo "   sudo tail -f /var/log/xl2tpd.log"
echo ""
echo "4. Test L2TP connection from client"

if [ $ISSUES -eq 0 ]; then
    echo ""
    print_success "Configuration verification completed - all good!"
else
    echo ""
    print_warning "Configuration needs fixes - run fix-config-quick.sh"
fi
