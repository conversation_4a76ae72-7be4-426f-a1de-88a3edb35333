#!/bin/bash

# Fix IP authorization issues for HTTP Authentication Plugin
# This script resolves the "<PERSON><PERSON> is not authorized to use remote address" error

set -e

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    print_error "This script needs to be run with sudo"
    echo "Usage: sudo ./fix-ip-authorization.sh"
    exit 1
fi

print_header "Fixing IP Authorization Issues"
echo "Problem: <PERSON>eer is not authorized to use remote address"
echo ""

# Step 1: Update PPP options to allow IP assignment
print_header "Step 1: Updating PPP Configuration"

# Backup original file
if [ -f "/etc/ppp/options.xl2tpd" ]; then
    cp /etc/ppp/options.xl2tpd /etc/ppp/options.xl2tpd.backup
    print_success "Backed up original configuration"
fi

# Get plugin path
PPPD_VERSION=$(pppd --version 2>&1 | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+' | head -1 || echo "2.4.9")
PLUGIN_PATH="/usr/lib/pppd/$PPPD_VERSION/httpauth.so"

# Create updated PPP options
cat > /etc/ppp/options.xl2tpd << EOF
# PPP options for xl2tpd with HTTP authentication
# Updated to fix IP authorization issues

# Authentication settings
require-pap
refuse-chap
refuse-mschap
refuse-mschap-v2
refuse-eap

# HTTP Authentication Plugin
plugin $PLUGIN_PATH
auth-url https://testapi.softapi.cn/notify/pcm_ok
sign-key hYC0ztcOKp2aZ5t0

# IP assignment and authorization settings
noauth
noipdefault
ipcp-accept-local
ipcp-accept-remote

# Allow any IP addresses (fix for authorization error)
# This allows xl2tpd to assign IPs from its configured range
+ipv6

# Network settings
proxyarp
nobsdcomp
nodeflate

# DNS settings
ms-dns *******
ms-dns *******

# Connection settings
lcp-echo-interval 30
lcp-echo-failure 4
idle 1800

# Logging
logfile /var/log/xl2tpd.log

# Debug options (uncomment for troubleshooting)
# debug
# dump
EOF

print_success "Updated /etc/ppp/options.xl2tpd"

# Step 2: Update xl2tpd configuration
print_header "Step 2: Updating xl2tpd Configuration"

# Create proper xl2tpd configuration
cat > /etc/xl2tpd/xl2tpd.conf << 'EOF'
; xl2tpd configuration with HTTP authentication
; Updated to fix IP authorization issues

[global]
listen-addr = 0.0.0.0
port = 1701
auth file = /etc/xl2tpd/l2tp-secrets

[lns default]
; Server name
name = HTTPAuthVPNServer

; IP range for client assignment
ip range = ***********-***********0
local ip = **********

; Authentication settings
require chap = no
refuse pap = no
require authentication = yes

; PPP options file
pppoptfile = /etc/ppp/options.xl2tpd

; L2TP settings
length bit = yes
challenge = no
exclusive = no
tunnel authentication = no

; Session settings
EOF

print_success "Updated /etc/xl2tpd/xl2tpd.conf"

# Step 3: Create l2tp-secrets file (even though we use HTTP auth)
print_header "Step 3: Creating L2TP Secrets File"

# Create a minimal l2tp-secrets file
cat > /etc/xl2tpd/l2tp-secrets << 'EOF'
# L2TP secrets file
# This file is required by xl2tpd but authentication is handled by HTTP plugin
# Format: username server secret IP
* * * *
EOF

chmod 600 /etc/xl2tpd/l2tp-secrets
print_success "Created /etc/xl2tpd/l2tp-secrets"

# Step 4: Create IP pool configuration
print_header "Step 4: Configuring IP Pool"

# Ensure the IP range is properly configured
echo "IP range configured: ***********-***********0"
echo "Server IP: **********"
print_success "IP pool configuration completed"

# Step 5: Restart xl2tpd service
print_header "Step 5: Restarting xl2tpd Service"

if systemctl is-active xl2tpd >/dev/null 2>&1; then
    systemctl restart xl2tpd
    print_success "xl2tpd service restarted"
else
    systemctl start xl2tpd
    print_success "xl2tpd service started"
fi

# Enable service for auto-start
systemctl enable xl2tpd >/dev/null 2>&1
print_success "xl2tpd service enabled for auto-start"

# Step 6: Verify configuration
print_header "Step 6: Verifying Configuration"

# Check if xl2tpd is running
if systemctl is-active xl2tpd >/dev/null 2>&1; then
    print_success "xl2tpd service is running"
else
    print_error "xl2tpd service failed to start"
    echo "Check logs with: sudo journalctl -u xl2tpd -f"
fi

# Check if port is listening
if netstat -ulnp 2>/dev/null | grep -q ":1701"; then
    print_success "xl2tpd is listening on port 1701"
else
    print_warning "xl2tpd may not be listening on port 1701"
fi

# Step 7: Display configuration summary
print_header "Configuration Summary"

echo ""
echo "📋 Updated configurations:"
echo "   ✓ /etc/ppp/options.xl2tpd - Fixed IP authorization"
echo "   ✓ /etc/xl2tpd/xl2tpd.conf - Updated IP range"
echo "   ✓ /etc/xl2tpd/l2tp-secrets - Created secrets file"
echo ""
echo "🌐 Network configuration:"
echo "   Server IP: **********"
echo "   Client IP range: ***********-***********0"
echo "   DNS servers: *******, *******"
echo ""
echo "🔧 Key changes made:"
echo "   ✓ Added 'noauth' to allow IP assignment"
echo "   ✓ Added 'ipcp-accept-local' and 'ipcp-accept-remote'"
echo "   ✓ Configured proper IP range in xl2tpd"
echo "   ✓ Created required l2tp-secrets file"
echo ""

print_header "Testing Instructions"

echo ""
echo "🧪 To test the fix:"
echo ""
echo "1. Test authentication:"
echo "   test_auth test001 password"
echo ""
echo "2. Check xl2tpd logs:"
echo "   sudo tail -f /var/log/xl2tpd.log"
echo ""
echo "3. Monitor PPP connections:"
echo "   sudo tail -f /var/log/syslog | grep pppd"
echo ""
echo "4. Test L2TP connection from client"
echo ""
echo "5. If issues persist, enable debug:"
echo "   Add 'debug' to /etc/ppp/options.xl2tpd"
echo ""

print_header "Troubleshooting"

echo ""
echo "🔍 If you still see authorization errors:"
echo ""
echo "1. Check IP range conflicts:"
echo "   ip route show"
echo ""
echo "2. Verify firewall settings:"
echo "   sudo ufw status"
echo "   sudo ufw allow 1701/udp"
echo ""
echo "3. Check for IP forwarding:"
echo "   echo 'net.ipv4.ip_forward=1' | sudo tee -a /etc/sysctl.conf"
echo "   sudo sysctl -p"
echo ""

print_success "IP authorization fix completed!"

echo ""
echo "The 'Peer is not authorized to use remote address' error should now be resolved."
echo "Try connecting again and monitor the logs for successful IP assignment."
