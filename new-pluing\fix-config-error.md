# 修复PPP配置错误

## 问题分析

错误信息：`unrecognized option '/dev/ttyUSB0'` 表明pppd不认识设备路径作为配置选项。

在Ubuntu 24.04中，pppd的配置格式有特定要求：

## 解决方案

### 方案1：使用正确的配置格式

运行配置修复脚本：
```bash
sudo ./create-peer-config.sh
```

### 方案2：手动修复配置

编辑 `/etc/ppp/peers/httpauth` 文件：

**错误的格式：**
```
/dev/ttyUSB0
115200
plugin /usr/lib/pppd/2.4.9/httpauth.so
# ... 其他选项
```

**正确的格式：**
```
# HTTP Authentication Peer Configuration
115200
defaultroute
usepeerdns
persist
maxfail 0
asyncmap 0
auth
crtscts
lock
hide-password
noipdefault

# HTTP Authentication Plugin
plugin /usr/lib/pppd/2.4.9/httpauth.so
auth-url https://testapi.softapi.cn/notify/pcm_ok
sign-key hYC0ztcOKp2aZ5t0

# 设备路径应该在命令行指定，不在配置文件中
```

### 方案3：使用命令行指定设备

```bash
# 方法1：在命令行指定设备
sudo pppd call httpauth /dev/ttyUSB0

# 方法2：直接使用完整命令
sudo pppd /dev/ttyUSB0 115200 \
  plugin /usr/lib/pppd/2.4.9/httpauth.so \
  auth-url https://testapi.softapi.cn/notify/pcm_ok \
  sign-key hYC0ztcOKp2aZ5t0 \
  defaultroute usepeerdns
```

## 测试步骤

### 1. 检查可用设备
```bash
ls -la /dev/tty*
```

常见设备：
- `/dev/ttyUSB0` - USB串口设备
- `/dev/ttyS0` - 串口设备
- `/dev/ttyACM0` - USB调制解调器

### 2. 测试插件加载
```bash
sudo ./test-plugin-only.sh
```

### 3. 创建正确配置
```bash
sudo ./create-peer-config.sh
```

### 4. 测试连接

**选项A：使用修复后的配置**
```bash
sudo pppd call httpauth /dev/ttyUSB0
```

**选项B：使用包含设备的配置**
```bash
sudo pppd call httpauth-usb
```

**选项C：调试模式**
```bash
sudo pppd call httpauth-debug
tail -f /tmp/ppp-httpauth.log
```

## 配置文件示例

### 基本配置 (`/etc/ppp/peers/httpauth`)
```
115200
defaultroute
usepeerdns
persist
maxfail 0
asyncmap 0
auth
crtscts
lock
hide-password
noipdefault

plugin /usr/lib/pppd/2.4.9/httpauth.so
auth-url https://testapi.softapi.cn/notify/pcm_ok
sign-key hYC0ztcOKp2aZ5t0
```

### 包含设备的配置 (`/etc/ppp/peers/httpauth-usb`)
```
/dev/ttyUSB0
115200
defaultroute
usepeerdns
persist
maxfail 0
asyncmap 0
auth
crtscts
lock
hide-password
noipdefault

plugin /usr/lib/pppd/2.4.9/httpauth.so
auth-url https://testapi.softapi.cn/notify/pcm_ok
sign-key hYC0ztcOKp2aZ5t0
```

### 调试配置 (`/etc/ppp/peers/httpauth-debug`)
```
/dev/ttyUSB0
115200
defaultroute
usepeerdns
persist
maxfail 0
asyncmap 0
auth
crtscts
lock
hide-password
noipdefault

plugin /usr/lib/pppd/2.4.9/httpauth.so
auth-url https://testapi.softapi.cn/notify/pcm_ok
sign-key hYC0ztcOKp2aZ5t0

debug
logfile /tmp/ppp-httpauth.log
```

## 使用方法

### 1. 基本使用
```bash
sudo pppd call httpauth /dev/ttyUSB0
```

### 2. 使用预配置设备
```bash
sudo pppd call httpauth-usb
```

### 3. 调试模式
```bash
sudo pppd call httpauth-debug
```

### 4. 查看日志
```bash
# 系统日志
sudo tail -f /var/log/syslog | grep pppd

# 调试日志
tail -f /tmp/ppp-httpauth.log
```

## 故障排除

### 1. 插件未找到
```bash
# 检查插件是否存在
ls -la /usr/lib/pppd/*/httpauth.so

# 重新安装插件
sudo make -f Makefile.simple install
```

### 2. 设备不存在
```bash
# 检查设备
ls -la /dev/tty*

# 检查USB设备
lsusb
dmesg | grep tty
```

### 3. 权限问题
```bash
# 检查设备权限
ls -la /dev/ttyUSB0

# 添加用户到dialout组
sudo usermod -a -G dialout $USER
```

### 4. 认证失败
```bash
# 测试认证API
./test_auth username password

# 检查网络连接
curl -X POST https://testapi.softapi.cn/notify/pcm_ok \
     -d "u=test&p=test"
```

## 快速修复命令

```bash
# 1. 创建正确的配置文件
sudo ./create-peer-config.sh

# 2. 测试插件功能
sudo ./test-plugin-only.sh

# 3. 使用正确的命令启动
sudo pppd call httpauth-usb

# 或者直接指定设备
sudo pppd call httpauth /dev/ttyUSB0
```
