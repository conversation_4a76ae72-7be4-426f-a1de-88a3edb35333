<?php
/**
 * HTTP Authentication Server Example
 * Updated for u/p parameters
 * 
 * This script handles authentication requests from the HTTP auth plugin
 * with the updated parameter names: u (username) and p (password)
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Configuration
$sign_key = 'hYC0ztcOKp2aZ5t0';
$debug_mode = true; // Set to false in production

// Log function for debugging
function debug_log($message) {
    global $debug_mode;
    if ($debug_mode) {
        error_log("[HTTP Auth] " . $message);
    }
}

// Get POST parameters (updated parameter names)
$username = $_POST['u'] ?? '';
$password = $_POST['p'] ?? '';

debug_log("Authentication request for user: $username");

// Validate input
if (empty($username) || empty($password)) {
    debug_log("Missing username or password");
    echo json_encode([
        'pass' => 0,
        't' => time(),
        's' => md5('error' . time() . $sign_key)
    ]);
    exit;
}

// Your authentication logic here
// This is a simple example - replace with your actual authentication
function authenticate_user($username, $password) {
    // Example authentication logic
    $valid_users = [
        'testuser' => 'testpass',
        'admin' => 'admin123',
        'user1' => 'password1',
        'demo' => 'demo'
    ];
    
    return isset($valid_users[$username]) && $valid_users[$username] === $password;
}

// Perform authentication
$authenticated = authenticate_user($username, $password);

// Generate timestamp
$timestamp = time();

// Calculate signature: md5(username + password + timestamp + sign_key)
$signature_input = $username . $password . $timestamp . $sign_key;
$signature = md5($signature_input);

debug_log("Signature input: $signature_input");
debug_log("Calculated signature: $signature");
debug_log("Authentication result: " . ($authenticated ? 'SUCCESS' : 'FAILED'));

// Prepare response
$response = [
    'pass' => $authenticated ? 1 : 0,
    't' => $timestamp,
    's' => $signature
];

// Add debug information if enabled
if ($debug_mode) {
    $response['debug'] = [
        'username' => $username,
        'timestamp' => $timestamp,
        'signature_input' => $signature_input,
        'auth_result' => $authenticated ? 'success' : 'failed'
    ];
}

// Return JSON response
echo json_encode($response);

debug_log("Response sent: " . json_encode($response));
?>
