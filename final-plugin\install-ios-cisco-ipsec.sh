#!/bin/bash

# 苹果设备Cisco IPSec完整安装脚本
# 支持IP、用户名、密码、PSK认证

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_info() {
    echo -e "${CYAN}ℹ $1${NC}"
}

# 检查root权限
if [ "$EUID" -ne 0 ]; then
    print_error "此脚本需要sudo权限运行"
    echo "用法: sudo ./install-ios-cisco-ipsec.sh"
    exit 1
fi

print_header "苹果设备Cisco IPSec完整安装"
echo "支持: IP分配 + 用户名密码认证 + PSK认证"
echo "日期: $(date)"
echo ""

# 步骤1: 安装strongSwan
print_header "步骤1: 安装strongSwan"

if ! command -v ipsec &> /dev/null; then
    print_info "安装strongSwan..."
    apt-get update -qq
    apt-get install -y strongswan strongswan-pki libcharon-extra-plugins
    print_success "strongSwan安装完成"
else
    print_success "strongSwan已安装"
fi

# 步骤2: 配置IPSec
print_header "步骤2: 配置IPSec"

# 备份现有配置
if [ -f "/etc/ipsec.conf" ]; then
    cp /etc/ipsec.conf /etc/ipsec.conf.backup.$(date +%Y%m%d_%H%M%S)
    print_success "已备份现有IPSec配置"
fi

# 创建新的IPSec配置
cat > /etc/ipsec.conf << 'EOF'
config setup
  charondebug="ike 2, knl 1, cfg 0"
  uniqueids=no

conn %default
  ikelifetime=24h
  salifetime=24h
  rekey=no
  keyingtries=1
  dpdaction=clear
  dpddelay=300s
  dpdtimeout=300s

# 苹果设备Cisco IPSec配置
conn ios-cisco-ipsec
  auto=add
  left=%any
  leftid=*************
  leftsubnet=0.0.0.0/0
  right=%any
  rightsubnet=********/16
  type=tunnel
  
  # 苹果兼容的认证方式
  keyexchange=ikev1
  authby=xauthpsk
  xauth=server
  
  # 模式配置 (IP分配)
  leftmodecfgserver=yes
  rightmodecfgclient=yes
  modecfgpull=yes
  modecfgdns="*******, *******"
  leftaddresspool=**********-**********
  
  # 苹果设备兼容的加密算法
  ike=aes256-sha1-modp1024,aes128-sha1-modp1024,3des-sha1-modp1024!
  esp=aes256-sha1,aes128-sha1,3des-sha1!
  
  # 苹果设备必需的选项
  forceencaps=yes
  fragmentation=yes
  ikev2=never
  mobike=no
  
  # 兼容性选项
  sha2-truncbug=no
  cisco-unity=yes

# L2TP over IPSec (保持兼容)
conn l2tp-cisco
  auto=add
  left=%any
  leftid=*************
  keyexchange=ikev1
  authby=secret
  leftprotoport=udp/l2tp
  leftsubnet=0.0.0.0/0
  right=%any
  rightprotoport=%any
  type=transport
  ike=aes256-sha1-modp1024,aes128-sha1-modp1024,3des-sha1-modp1024!
  esp=aes256-sha1,aes128-sha1,3des-sha1!
  rekey=no
  fragmentation=yes
  forceencaps=yes
  ikev2=never
  sha2-truncbug=no
EOF

print_success "IPSec配置已创建"

# 步骤3: 配置认证
print_header "步骤3: 配置认证"

# 备份现有secrets
if [ -f "/etc/ipsec.secrets" ]; then
    cp /etc/ipsec.secrets /etc/ipsec.secrets.backup.$(date +%Y%m%d_%H%M%S)
    print_success "已备份现有secrets配置"
fi

# 创建认证配置
cat > /etc/ipsec.secrets << 'EOF'
# L2TP PSK
************* %any : PSK "L2TP-PSK-Key-2024"

# Cisco IPSec PSK (苹果设备使用)
%any %any : PSK "Cisco-IPSec-PSK-2024"

# XAuth用户认证 (HTTP认证插件处理)
# 这些用户将通过HTTP API验证
test001 : XAUTH "password123"
test002 : XAUTH "password456"
admin : XAUTH "admin123"
EOF

print_success "认证配置已创建"

# 步骤4: 安装HTTP认证脚本
print_header "步骤4: 安装HTTP认证脚本"

# 安装XAuth认证脚本
cp ipsec-xauth-http.sh /usr/local/bin/
chmod +x /usr/local/bin/ipsec-xauth-http.sh
print_success "XAuth HTTP认证脚本已安装"

# 创建日志文件
touch /var/log/ipsec-xauth.log
chmod 644 /var/log/ipsec-xauth.log
print_success "认证日志文件已创建"

# 步骤5: 配置网络
print_header "步骤5: 配置网络"

# 启用IP转发
echo 'net.ipv4.ip_forward=1' >> /etc/sysctl.conf
echo 'net.ipv4.conf.all.accept_redirects=0' >> /etc/sysctl.conf
echo 'net.ipv4.conf.all.send_redirects=0' >> /etc/sysctl.conf
sysctl -p > /dev/null 2>&1

print_success "网络配置已更新"

# 步骤6: 配置防火墙
print_header "步骤6: 配置防火墙"

# 配置iptables规则
iptables -t nat -A POSTROUTING -s ********/16 -o eth0 -j MASQUERADE
iptables -A FORWARD -s ********/16 -j ACCEPT
iptables -A FORWARD -d ********/16 -j ACCEPT
iptables -A INPUT -p udp --dport 500 -j ACCEPT
iptables -A INPUT -p udp --dport 4500 -j ACCEPT
iptables -A INPUT -p udp --dport 1701 -j ACCEPT

# 保存iptables规则
if command -v iptables-save &> /dev/null; then
    iptables-save > /etc/iptables/rules.v4 2>/dev/null || true
fi

print_success "防火墙规则已配置"

# 步骤7: 启动服务
print_header "步骤7: 启动服务"

# 重启strongSwan
systemctl restart strongswan
systemctl enable strongswan

# 等待服务启动
sleep 3

if systemctl is-active strongswan >/dev/null 2>&1; then
    print_success "strongSwan服务启动成功"
else
    print_error "strongSwan服务启动失败"
    echo "检查日志: sudo journalctl -u strongswan -f"
fi

# 步骤8: 显示配置信息
print_header "安装完成！"

echo ""
echo -e "${GREEN}🍎 苹果设备Cisco IPSec配置成功！${NC}"
echo ""

print_header "苹果设备配置信息"
echo "📱 iOS设备VPN配置:"
echo "   类型: IPSec"
echo "   服务器: *************"
echo "   账户: test001"
echo "   密码: password123"
echo "   群组名称: (留空)"
echo "   密钥: Cisco-IPSec-PSK-2024"
echo ""

print_header "支持的认证方式"
echo "🔐 认证层级:"
echo "   1. IPSec层: PSK认证 (Cisco-IPSec-PSK-2024)"
echo "   2. 用户层: HTTP API认证 (用户名/密码)"
echo "   3. IP分配: **********-**********"
echo ""

print_header "测试和调试"
echo "🔍 调试命令:"
echo "   查看IPSec状态: sudo ipsec status"
echo "   查看连接详情: sudo ipsec statusall"
echo "   监控认证日志: sudo tail -f /var/log/ipsec-xauth.log"
echo "   监控系统日志: sudo journalctl -u strongswan -f"
echo ""

print_header "预期连接流程"
echo "📋 连接步骤:"
echo "   1. 客户端连接*************"
echo "   2. IPSec协商 (使用PSK: Cisco-IPSec-PSK-2024)"
echo "   3. XAuth用户认证 (HTTP API验证用户名/密码)"
echo "   4. 分配IP地址 (**********-200范围)"
echo "   5. 建立隧道连接"
echo ""

print_success "苹果设备Cisco IPSec安装完成！"
echo ""
echo "现在可以在iOS设备上配置VPN连接了！"
