# HTTP Authentication Plugin Configuration Examples
# Ubuntu 24.04

# ============================================================================
# 1. Global PPP Configuration (/etc/ppp/options)
# ============================================================================

# Basic PPP settings
asyncmap 0
auth
crtscts
lock
hide-password
modem
proxyarp
lcp-echo-interval 30
lcp-echo-failure 4

# HTTP Authentication Plugin
plugin /usr/lib/pppd/2.4.9/httpauth.so
auth-url https://testapi.softapi.cn/notify/pcm_ok
sign-key hYC0ztcOKp2aZ5t0

# DNS settings
ms-dns 8.8.8.8
ms-dns 8.8.4.4

# ============================================================================
# 2. Peer Configuration (/etc/ppp/peers/httpauth-modem)
# ============================================================================

# Serial device and speed
/dev/ttyUSB0
115200

# HTTP Authentication
plugin /usr/lib/pppd/2.4.9/httpauth.so
auth-url https://testapi.softapi.cn/notify/pcm_ok
sign-key hYC0ztcOKp2aZ5t0

# Connection settings
defaultroute
usepeerdns
persist
maxfail 0

# PPP options
asyncmap 0
auth
crtscts
lock
hide-password

# ============================================================================
# 3. Custom Server Configuration (/etc/ppp/peers/custom-server)
# ============================================================================

# For custom authentication server
/dev/ttyUSB0
115200

plugin /usr/lib/pppd/2.4.9/httpauth.so
auth-url https://your-server.com/api/ppp/auth
sign-key yourCustomSignKey123

defaultroute
usepeerdns
persist
maxfail 0
asyncmap 0
auth
crtscts
lock
hide-password

# ============================================================================
# 4. Debug Configuration (/etc/ppp/peers/httpauth-debug)
# ============================================================================

# For troubleshooting
/dev/ttyUSB0
115200

plugin /usr/lib/pppd/2.4.9/httpauth.so
auth-url https://testapi.softapi.cn/notify/pcm_ok
sign-key hYC0ztcOKp2aZ5t0

# Debug options
debug
logfile /tmp/ppp-debug.log

defaultroute
usepeerdns
persist
maxfail 0
asyncmap 0
auth
crtscts
lock
hide-password

# ============================================================================
# Usage Commands
# ============================================================================

# Start connection using peer configuration:
# sudo pppd call httpauth-modem

# Start connection with direct parameters:
# sudo pppd /dev/ttyUSB0 115200 \
#     plugin /usr/lib/pppd/2.4.9/httpauth.so \
#     auth-url https://testapi.softapi.cn/notify/pcm_ok \
#     sign-key hYC0ztcOKp2aZ5t0 \
#     defaultroute usepeerdns

# Start with debug:
# sudo pppd call httpauth-debug

# Test authentication manually:
# ./test_auth username password

# ============================================================================
# Server API Requirements
# ============================================================================

# Your authentication server must:
# 1. Accept POST requests to the specified auth-url
# 2. Accept parameters: u (username), p (password)
# 3. Return JSON response:
#    {
#        "pass": 1,                    // 1 = success, other = failure
#        "t": 1752454664,             // Unix timestamp
#        "s": "md5_signature_here"    // MD5 signature
#    }
# 4. Calculate signature as: md5(username + password + timestamp + sign_key)

# Example server response for successful authentication:
# {
#     "pass": 1,
#     "t": 1752454664,
#     "s": "673261d844d929b52a468404aac290ca"
# }

# ============================================================================
# Troubleshooting
# ============================================================================

# Check plugin installation:
# ls -la /usr/lib/pppd/*/httpauth.so

# Check plugin dependencies:
# ldd /usr/lib/pppd/2.4.9/httpauth.so

# View PPP logs:
# sudo tail -f /var/log/syslog | grep pppd

# Test plugin loading:
# sudo pppd plugin /usr/lib/pppd/2.4.9/httpauth.so nodetach debug

# Check network connectivity:
# curl -X POST https://testapi.softapi.cn/notify/pcm_ok \
#      -d "username=testuser&password=testpass"

# ============================================================================
# Security Notes
# ============================================================================

# 1. Always use HTTPS for authentication URLs in production
# 2. Keep your sign-key secret and unique
# 3. Implement rate limiting on your authentication server
# 4. Monitor authentication logs for security
# 5. Consider using certificate-based authentication for additional security
# 6. Rotate sign-key periodically
# 7. Validate input on server side to prevent injection attacks
