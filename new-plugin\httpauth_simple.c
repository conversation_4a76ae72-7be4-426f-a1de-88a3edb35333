/***********************************************************************
*
* httpauth_simple.c
*
* HTTP URL authentication plugin for pppd - Simplified version
*
* Copyright (C) 2024
*
***********************************************************************/

#define _GNU_SOURCE
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <time.h>
#include <curl/curl.h>
#include <json-c/json.h>
#include <openssl/evp.h>

/* Define BSD types for pppd compatibility */
typedef unsigned char u_char;
typedef unsigned short u_short;
typedef unsigned int u_int;
typedef unsigned long u_long;

#include <pppd/pppd.h>
#include <pppd/chap-new.h>

#define BUF_LEN 1024
#define SIGN_KEY "hYC0ztcOKp2aZ5t0"
#define DEFAULT_AUTH_URL "https://testapi.softapi.cn/notify/pcm_ok"

/* Configuration options */
static char *auth_url = NULL;
static char *sign_key = NULL;

/* HTTP response structure */
struct http_response {
    char *data;
    size_t size;
};

static option_t httpauth_options[] = {
    { "auth-url", o_string, &auth_url,
      "HTTP authentication URL" },
    { "sign-key", o_string, &sign_key,
      "Signature key for authentication" },
    { NULL }
};

/* Function prototypes */
static int httpauth_secret_check(void);
static int httpauth_pap_auth(char *user, char *passwd, char **msgp,
                            struct wordlist **paddrs, struct wordlist **popts);
static size_t write_callback(void *contents, size_t size, size_t nmemb, struct http_response *response);
static char *calculate_md5(const char *input);
static int verify_signature(const char *username, const char *password, 
                           time_t timestamp, const char *received_sig);
static int send_http_request(const char *username, const char *password, char *response_msg);

char pppd_version[] = VERSION;

/**********************************************************************
* Plugin initialization
***********************************************************************/
void plugin_init(void)
{
    /* Set default values */
    if (!auth_url) {
        auth_url = strdup(DEFAULT_AUTH_URL);
    }
    if (!sign_key) {
        sign_key = strdup(SIGN_KEY);
    }

    /* Initialize curl */
    curl_global_init(CURL_GLOBAL_DEFAULT);

    /* Hook into pppd authentication */
    pap_check_hook = httpauth_secret_check;
    pap_auth_hook = httpauth_pap_auth;

    /* Add our options */
    add_options(httpauth_options);

    info("HTTP authentication plugin initialized.");
}

/**********************************************************************
* Secret check - always return 1 (we handle authentication)
***********************************************************************/
static int httpauth_secret_check(void)
{
    return 1;
}

/**********************************************************************
* HTTP response callback
***********************************************************************/
static size_t write_callback(void *contents, size_t size, size_t nmemb, struct http_response *response)
{
    size_t realsize = size * nmemb;
    char *ptr = realloc(response->data, response->size + realsize + 1);
    
    if (!ptr) {
        error("HTTP Auth: Memory allocation failed");
        return 0;
    }
    
    response->data = ptr;
    memcpy(&(response->data[response->size]), contents, realsize);
    response->size += realsize;
    response->data[response->size] = 0;
    
    return realsize;
}

/**********************************************************************
* Calculate MD5 hash
***********************************************************************/
static char *calculate_md5(const char *input)
{
    EVP_MD_CTX *mdctx;
    const EVP_MD *md;
    unsigned char digest[EVP_MAX_MD_SIZE];
    unsigned int digest_len;
    char *hash_string;
    
    hash_string = malloc(33);
    if (!hash_string) {
        return NULL;
    }
    
    md = EVP_md5();
    mdctx = EVP_MD_CTX_new();
    
    if (!mdctx) {
        free(hash_string);
        return NULL;
    }
    
    EVP_DigestInit_ex(mdctx, md, NULL);
    EVP_DigestUpdate(mdctx, input, strlen(input));
    EVP_DigestFinal_ex(mdctx, digest, &digest_len);
    EVP_MD_CTX_free(mdctx);
    
    for (unsigned int i = 0; i < digest_len; i++) {
        sprintf(&hash_string[i*2], "%02x", digest[i]);
    }
    
    return hash_string;
}

/**********************************************************************
* Verify signature
***********************************************************************/
static int verify_signature(const char *username, const char *password, 
                           time_t timestamp, const char *received_sig)
{
    char input[512];
    char *calculated_sig;
    int result = 0;
    
    snprintf(input, sizeof(input), "%s%s%ld%s", 
             username, password, timestamp, sign_key);
    
    calculated_sig = calculate_md5(input);
    if (calculated_sig) {
        result = (strcmp(calculated_sig, received_sig) == 0);
        free(calculated_sig);
    }
    
    return result;
}

/**********************************************************************
* Send HTTP request
***********************************************************************/
static int send_http_request(const char *username, const char *password, char *response_msg)
{
    CURL *curl;
    CURLcode res;
    struct http_response response = {0};
    char post_data[512];
    json_object *json_resp, *pass_obj, *t_obj, *s_obj;
    int pass_value = 0;
    time_t timestamp = 0;
    const char *signature = NULL;
    int auth_result = 0;
    
    curl = curl_easy_init();
    if (!curl) {
        strcpy(response_msg, "HTTP Auth: Failed to initialize curl");
        return 0;
    }
    
    /* Prepare POST data */
    snprintf(post_data, sizeof(post_data), "username=%s&password=%s", username, password);
    
    /* Set curl options */
    curl_easy_setopt(curl, CURLOPT_URL, auth_url);
    curl_easy_setopt(curl, CURLOPT_POSTFIELDS, post_data);
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, write_callback);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 30L);
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);
    
    /* Perform the request */
    res = curl_easy_perform(curl);
    curl_easy_cleanup(curl);
    
    if (res != CURLE_OK) {
        snprintf(response_msg, BUF_LEN, "HTTP Auth: Request failed: %s", curl_easy_strerror(res));
        if (response.data) free(response.data);
        return 0;
    }
    
    if (!response.data) {
        strcpy(response_msg, "HTTP Auth: Empty response from server");
        return 0;
    }
    
    /* Parse JSON response */
    json_resp = json_tokener_parse(response.data);
    if (!json_resp) {
        strcpy(response_msg, "HTTP Auth: Invalid JSON response");
        free(response.data);
        return 0;
    }
    
    /* Extract values from JSON */
    if (json_object_object_get_ex(json_resp, "pass", &pass_obj)) {
        pass_value = json_object_get_int(pass_obj);
    }
    
    if (json_object_object_get_ex(json_resp, "t", &t_obj)) {
        timestamp = json_object_get_int64(t_obj);
    }
    
    if (json_object_object_get_ex(json_resp, "s", &s_obj)) {
        signature = json_object_get_string(s_obj);
    }
    
    /* Verify signature */
    if (signature && verify_signature(username, password, timestamp, signature)) {
        if (pass_value == 1) {
            auth_result = 1;
            strcpy(response_msg, "Authentication successful");
        } else {
            snprintf(response_msg, BUF_LEN, "Authentication failed: pass=%d", pass_value);
        }
    } else {
        strcpy(response_msg, "HTTP Auth: Signature verification failed");
    }
    
    /* Cleanup */
    json_object_put(json_resp);
    free(response.data);
    
    return auth_result;
}

/**********************************************************************
* PAP authentication
***********************************************************************/
static int httpauth_pap_auth(char *user, char *passwd, char **msgp,
                            struct wordlist **paddrs, struct wordlist **popts)
{
    static char response_msg[BUF_LEN];
    int result;
    
    /* Suppress unused parameter warnings */
    (void)paddrs;
    (void)popts;
    
    response_msg[0] = 0;
    *msgp = response_msg;
    
    if (!user || !passwd) {
        strcpy(response_msg, "HTTP Auth: Missing username or password");
        return 0;
    }
    
    info("HTTP Auth: Authenticating user '%s'", user);
    
    result = send_http_request(user, passwd, response_msg);
    
    if (result) {
        info("HTTP Auth: User '%s' authenticated successfully", user);
    } else {
        warn("HTTP Auth: Authentication failed for user '%s': %s", user, response_msg);
    }
    
    return result;
}
