# General settings

# specify which authentication comes first respectively which
# authentication is used. possible values are: "radius" and "local".
# if you specify "radius,local" then the RADIUS server is asked
# first then the local one. if only one keyword is specified only
# this server is asked.
auth_order	radius

# maximum login tries a user has (default 4)
login_tries	4

# timeout for all login tries (default 60)
# if this time is exceeded the user is kicked out 
login_timeout	60

# name of the nologin file which when it exists disables logins.
# it may be extended by the ttyname which will result in
# a terminal specific lock (e.g. /etc/nologin.ttyS2 will disable
# logins on /dev/ttyS2)   (default /etc/nologin)
nologin /etc/nologin

# name of the issue file. it's only display when no username is passed
# on the radlogin command line  (default /etc/radiusclient/issue)
issue	/usr/local/etc/radiusclient/issue

# RADIUS settings

# RADIUS server to use for authentication requests. this config
# item can appear more then one time. if multiple servers are
# defined they are tried in a round robin fashion if one
# server is not answering.
# optionally you can specify a the port number on which is remote
# RADIUS listens separated by a colon from the hostname. if
# no port is specified /etc/services is consulted of the radius
# service. if this fails also a compiled in default is used.
authserver 	localhost:1812

# RADIUS server to use for accouting requests. All that I
# said for authserver applies, too. 
#
acctserver 	localhost:1813

# file holding shared secrets used for the communication
# between the RADIUS client and server
servers		/usr/local/etc/radiusclient/servers

# dictionary of allowed attributes and values
# just like in the normal RADIUS distributions
dictionary 	/usr/local/etc/radiusclient/dictionary

# program to call for a RADIUS authenticated login 
# (default /usr/sbin/login.radius)
login_radius	/usr/local/sbin/login.radius

# file which holds sequence number for communication with the
# RADIUS server
seqfile		/var/run/radius.seq

# file which specifies mapping between ttyname and NAS-Port attribute
mapfile		/usr/local/etc/radiusclient/port-id-map

# default authentication realm to append to all usernames if no
# realm was explicitly specified by the user
# the radiusd directly form Livingston doesnt use any realms, so leave
# it blank then
default_realm

# time to wait for a reply from the RADIUS server
radius_timeout	10

# resend request this many times before trying the next server
radius_retries	3

# NAS-Identifier
#
# If supplied, this option will cause the client to send the given string
# as the contents of the NAS-Identifier attribute in RADIUS requests.  No
# NAS-IP-Address attribute will be sent in this case.
#
# The default behavior is to send a NAS-IP-Address option and not send
# a NAS-Identifier.  The value of the NAS-IP-Address option is chosen
# by resolving the system hostname.

# nas_identifier MyUniqueNASName

# LOCAL settings

# program to execute for local login
# it must support the -f flag for preauthenticated login
login_local	/bin/login
