# HTTP Authentication Plugin Configuration Example for Ubuntu 24.04
#
# This file shows example configurations for the httpauth plugin
# Copy this file to your pppd configuration directory and modify as needed
#
# Ubuntu 24.04 specific paths:
# - PPP config: /etc/ppp/
# - Plugin directory: /usr/lib/pppd/[version]/
# - Man pages: /usr/share/man/man8/

# Basic configuration with default settings
# plugin httpauth.so

# Configuration with custom authentication URL
# plugin httpauth.so
# auth-url https://your-server.com/api/ppp/auth

# Configuration with custom signature key
# plugin httpauth.so
# sign-key yourCustomSignatureKey123

# Complete configuration example
# plugin httpauth.so
# auth-url https://api.example.com/ppp/authenticate
# sign-key mySecretKey456

# Example pppd configuration file (/etc/ppp/options) for Ubuntu 24.04
# ================================================================

# Basic PPP options
asyncmap 0
auth
crtscts
lock
hide-password
modem
proxyarp
lcp-echo-interval 30
lcp-echo-failure 4
noipx

# Load HTTP authentication plugin (Ubuntu 24.04 path)
plugin /usr/lib/pppd/2.4.9/httpauth.so
auth-url https://testapi.softapi.cn/notify/pcm_ok
sign-key hYC0ztcOKp2aZ5t0

# IP configuration
ms-dns *******
ms-dns *******

# Ubuntu 24.04 specific options
netmask *************
defaultroute
usepeerdns

# Example peer configuration file (/etc/ppp/peers/httpauth-test)
# ============================================================

# Serial device
/dev/ttyS0

# Connection speed
115200

# PPP options
defaultroute
usepeerdns
persist
maxfail 0

# Authentication
plugin httpauth.so
auth-url https://testapi.softapi.cn/notify/pcm_ok
sign-key hYC0ztcOKp2aZ5t0

# Server-side implementation notes
# ===============================

# Your authentication server should:
# 1. Accept POST requests with 'username' and 'password' parameters
# 2. Validate the credentials against your user database
# 3. Generate a timestamp (Unix timestamp)
# 4. Calculate signature: md5(username + password + timestamp + sign_key)
# 5. Return JSON response in the format:
#    {
#        "pass": 1,                    // 1 for success, other for failure
#        "t": 1752454664,             // timestamp
#        "s": "md5_signature_here"    // calculated signature
#    }

# Security considerations
# ======================

# 1. Always use HTTPS in production
# 2. Keep your signature key secret
# 3. Implement proper input validation on server side
# 4. Consider implementing rate limiting
# 5. Log authentication attempts for security monitoring
# 6. Rotate signature keys periodically

# Troubleshooting
# ==============

# Enable debug mode:
# debug
# plugin httpauth.so

# Check logs:
# tail -f /var/log/syslog | grep pppd

# Test authentication manually:
# ./test_auth username password
