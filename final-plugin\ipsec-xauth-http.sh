#!/bin/bash

# strongSwan XAuth HTTP认证脚本
# 集成HTTP认证插件，支持苹果设备Cisco IPSec

# 配置
AUTH_URL="https://testapi.softapi.cn/notify/pcm_ok"
SIGN_KEY="hYC0ztcOKp2aZ5t0"
LOG_FILE="/var/log/ipsec-xauth.log"

# 日志函数
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') [IPSEC-XAUTH] $1" >> "$LOG_FILE"
}

# MD5签名计算函数
calculate_md5() {
    echo -n "$1" | openssl dgst -md5 -hex | awk '{print $2}'
}

# HTTP认证函数
http_authenticate() {
    local username="$1"
    local password="$2"
    local timestamp=$(date +%s)
    local signature
    local post_data
    local response
    local pass_value
    
    log_message "开始HTTP认证: 用户=$username"
    
    # 计算签名
    signature=$(calculate_md5 "${username}${password}${timestamp}${SIGN_KEY}")
    
    # 准备POST数据
    post_data="u=${username}&p=${password}&t=${timestamp}&s=${signature}"
    
    log_message "发送认证请求: $post_data"
    
    # 发送HTTP请求
    response=$(curl -s -X POST \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "$post_data" \
        --connect-timeout 10 \
        --max-time 30 \
        "$AUTH_URL" 2>/dev/null)
    
    log_message "收到响应: $response"
    
    # 解析JSON响应
    if [ -n "$response" ]; then
        pass_value=$(echo "$response" | python3 -c "
import json, sys
try:
    data = json.load(sys.stdin)
    print(data.get('pass', 0))
except:
    print(0)
" 2>/dev/null)
        
        if [ "$pass_value" = "1" ]; then
            log_message "HTTP认证成功: 用户=$username"
            return 0
        else
            log_message "HTTP认证失败: 用户=$username, pass=$pass_value"
            return 1
        fi
    else
        log_message "HTTP认证失败: 无响应, 用户=$username"
        return 1
    fi
}

# 主认证逻辑
main() {
    local username="$1"
    local password="$2"
    
    # 记录认证请求
    log_message "=== XAuth认证请求 ==="
    log_message "用户名: $username"
    log_message "来源IP: ${PLUTO_PEER_CLIENT:-unknown}"
    log_message "连接名: ${PLUTO_CONNECTION:-unknown}"
    
    # 执行HTTP认证
    if http_authenticate "$username" "$password"; then
        log_message "XAuth认证成功: $username"
        echo "认证成功"
        exit 0
    else
        log_message "XAuth认证失败: $username"
        echo "认证失败"
        exit 1
    fi
}

# 检查参数
if [ $# -ne 2 ]; then
    log_message "错误: 参数不足, 需要用户名和密码"
    echo "用法: $0 <username> <password>"
    exit 1
fi

# 创建日志文件
touch "$LOG_FILE"
chmod 644 "$LOG_FILE"

# 执行认证
main "$1" "$2"
