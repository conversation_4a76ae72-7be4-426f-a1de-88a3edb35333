#!/bin/bash

# Quick test script for HTTP Authentication Plugin
# Ubuntu 24.04

set -e

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

echo "=== HTTP Authentication Plugin Test ==="
echo ""

# Test 1: Check dependencies
echo "1. Checking dependencies..."
DEPS_OK=true

for pkg in libcurl json-c openssl; do
    if pkg-config --exists $pkg; then
        print_success "$pkg: $(pkg-config --modversion $pkg)"
    else
        print_error "$pkg not found"
        DEPS_OK=false
    fi
done

if [ ! -d "/usr/include/pppd" ]; then
    print_error "pppd headers not found"
    DEPS_OK=false
else
    print_success "pppd headers found"
fi

if [ "$DEPS_OK" = false ]; then
    echo ""
    echo "Install dependencies with:"
    echo "  sudo apt-get install ppp-dev libcurl4-openssl-dev libjson-c-dev libssl-dev"
    exit 1
fi

echo ""

# Test 2: Build plugin
echo "2. Building plugin..."
if make clean && make; then
    print_success "Plugin built successfully"
    if [ -f "httpauth.so" ]; then
        echo "   Size: $(ls -lh httpauth.so | awk '{print $5}')"
    fi
else
    print_error "Plugin build failed"
    exit 1
fi

echo ""

# Test 3: Build test program
echo "3. Building test program..."
if make test; then
    print_success "Test program built successfully"
else
    print_error "Test program build failed"
    exit 1
fi

echo ""

# Test 4: Test authentication API
echo "4. Testing authentication API..."
if ./test_auth testuser testpass; then
    print_success "Authentication API test passed"
else
    print_warning "Authentication API test failed (server may be unreachable)"
fi

echo ""

# Test 5: Check plugin symbols
echo "5. Checking plugin symbols..."
if nm httpauth.so | grep -q "plugin_init"; then
    print_success "Plugin contains required symbols"
else
    print_error "Plugin missing required symbols"
fi

echo ""

# Test 6: Check dependencies
echo "6. Checking plugin dependencies..."
if ldd httpauth.so | grep -E "(curl|json|ssl)" > /dev/null; then
    print_success "Plugin dependencies OK"
    ldd httpauth.so | grep -E "(curl|json|ssl)" | sed 's/^/   /'
else
    print_warning "Could not verify plugin dependencies"
fi

echo ""

# Test 7: Installation check
echo "7. Checking installation readiness..."
PPPD_VERSION=$(pppd --version 2>&1 | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+' | head -1 || echo "2.4.9")
PLUGIN_DIR="/usr/lib/pppd/$PPPD_VERSION"

echo "   PPP version: $PPPD_VERSION"
echo "   Plugin directory: $PLUGIN_DIR"

if [ -d "$PLUGIN_DIR" ]; then
    print_success "Plugin directory exists"
else
    print_warning "Plugin directory will be created during installation"
fi

echo ""

# Summary
echo "=== Test Summary ==="
print_success "Dependencies: OK"
print_success "Plugin compilation: OK"
print_success "Test program: OK"
print_success "Plugin symbols: OK"

echo ""
echo "=== Next Steps ==="
echo ""
echo "1. Install the plugin:"
echo "   sudo ./install.sh"
echo ""
echo "2. Or install manually:"
echo "   sudo make install"
echo ""
echo "3. Test authentication:"
echo "   test_auth <username> <password>"
echo ""
echo "4. Configure xl2tpd:"
echo "   sudo cp options.xl2tpd.example /etc/ppp/options.xl2tpd"
echo "   sudo cp xl2tpd.conf.example /etc/xl2tpd/xl2tpd.conf"
echo ""
echo "5. Start xl2tpd service:"
echo "   sudo systemctl start xl2tpd"
echo ""
echo "Plugin is ready for installation and use!"
