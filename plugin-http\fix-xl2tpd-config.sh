#!/bin/bash

# Fix xl2tpd configuration errors
# Resolves "Unknown field 'tunnel authentication'" error

set -e

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    print_error "This script needs to be run with sudo"
    echo "Usage: sudo ./fix-xl2tpd-config.sh"
    exit 1
fi

print_header "Fixing xl2tpd Configuration Errors"
echo "Problem: Unknown field 'tunnel authentication'"
echo ""

# Step 1: Stop xl2tpd service
print_header "Step 1: Stopping xl2tpd Service"
systemctl stop xl2tpd 2>/dev/null || true
print_success "xl2tpd service stopped"

# Step 2: Backup existing configuration
print_header "Step 2: Backing Up Configuration"
if [ -f "/etc/xl2tpd/xl2tpd.conf" ]; then
    cp /etc/xl2tpd/xl2tpd.conf /etc/xl2tpd/xl2tpd.conf.backup.$(date +%Y%m%d_%H%M%S)
    print_success "Configuration backed up"
fi

# Step 3: Create corrected xl2tpd configuration
print_header "Step 3: Creating Corrected Configuration"

cat > /etc/xl2tpd/xl2tpd.conf << 'EOF'
; xl2tpd configuration for Ubuntu 24.04
; Compatible with xl2tpd version 1.3.18

[global]
; IP address to listen on
listen-addr = 0.0.0.0

; L2TP port
port = 1701

; Access control file (optional)
auth file = /etc/xl2tpd/l2tp-secrets

; Debug settings (set to no for production)
debug avp = no
debug network = no
debug packet = no
debug state = no
debug tunnel = no

[lns default]
; Server identification
name = HTTPAuthVPNServer

; IP address range for clients
ip range = ***********-***********0

; Local server IP
local ip = **********

; Authentication settings
require chap = no
refuse pap = no
require authentication = yes

; PPP options file
pppoptfile = /etc/ppp/options.xl2tpd

; L2TP protocol settings
length bit = yes
challenge = no
exclusive = no

; Redial settings
redial = yes
redial timeout = 30
max redials = 5
EOF

print_success "Created corrected xl2tpd configuration"

# Step 4: Ensure l2tp-secrets file exists
print_header "Step 4: Creating L2TP Secrets File"

cat > /etc/xl2tpd/l2tp-secrets << 'EOF'
# L2TP secrets file for xl2tpd
# This file is required but authentication is handled by HTTP plugin
# Format: username server secret IP
* * * *
EOF

chmod 600 /etc/xl2tpd/l2tp-secrets
print_success "Created l2tp-secrets file"

# Step 5: Verify PPP configuration
print_header "Step 5: Verifying PPP Configuration"

if [ -f "/etc/ppp/options.xl2tpd" ]; then
    print_success "PPP options file exists"
    
    # Check if it has the necessary options
    if ! grep -q "noauth" /etc/ppp/options.xl2tpd; then
        print_warning "Adding missing 'noauth' option to PPP config"
        echo "" >> /etc/ppp/options.xl2tpd
        echo "# IP authorization fix" >> /etc/ppp/options.xl2tpd
        echo "noauth" >> /etc/ppp/options.xl2tpd
        echo "ipcp-accept-local" >> /etc/ppp/options.xl2tpd
        echo "ipcp-accept-remote" >> /etc/ppp/options.xl2tpd
    fi
else
    print_error "PPP options file missing - creating it"
    
    # Get plugin path
    PPPD_VERSION=$(pppd --version 2>&1 | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+' | head -1 || echo "2.4.9")
    PLUGIN_PATH="/usr/lib/pppd/$PPPD_VERSION/httpauth.so"
    
    cat > /etc/ppp/options.xl2tpd << EOF
# PPP options for xl2tpd with HTTP authentication
require-pap
refuse-chap
refuse-mschap
refuse-mschap-v2
refuse-eap

# HTTP Authentication Plugin
plugin $PLUGIN_PATH
auth-url https://testapi.softapi.cn/notify/pcm_ok
sign-key hYC0ztcOKp2aZ5t0

# IP authorization settings
noauth
ipcp-accept-local
ipcp-accept-remote

# Network settings
proxyarp
nobsdcomp
nodeflate
noipdefault

# DNS settings
ms-dns *******
ms-dns *******

# Connection settings
lcp-echo-interval 30
lcp-echo-failure 4
idle 1800
logfile /var/log/xl2tpd.log
EOF
    
    print_success "Created PPP options file"
fi

# Step 6: Test configuration syntax
print_header "Step 6: Testing Configuration"

echo "Testing xl2tpd configuration syntax..."
if xl2tpd -D 2>&1 | grep -q "xl2tpd version"; then
    print_success "xl2tpd configuration syntax is valid"
else
    print_warning "Configuration test inconclusive"
fi

# Step 7: Start xl2tpd service
print_header "Step 7: Starting xl2tpd Service"

systemctl start xl2tpd

# Wait a moment for service to start
sleep 2

if systemctl is-active xl2tpd >/dev/null 2>&1; then
    print_success "xl2tpd service started successfully"
else
    print_error "xl2tpd service failed to start"
    echo "Checking logs..."
    journalctl -u xl2tpd --no-pager -n 5
fi

# Step 8: Enable service
systemctl enable xl2tpd >/dev/null 2>&1
print_success "xl2tpd service enabled for auto-start"

# Step 9: Verify service status
print_header "Step 9: Service Status Verification"

echo "Service status:"
systemctl status xl2tpd --no-pager -l

echo ""
echo "Port listening check:"
if netstat -ulnp 2>/dev/null | grep ":1701"; then
    print_success "xl2tpd is listening on port 1701"
    netstat -ulnp | grep ":1701"
else
    print_warning "xl2tpd may not be listening on port 1701"
fi

# Step 10: Display configuration summary
print_header "Configuration Fix Summary"

echo ""
echo "🔧 Fixed issues:"
echo "   ✓ Removed unsupported 'tunnel authentication' option"
echo "   ✓ Updated xl2tpd.conf for Ubuntu 24.04 compatibility"
echo "   ✓ Created proper l2tp-secrets file"
echo "   ✓ Added IP authorization options to PPP config"
echo ""
echo "📋 Current configuration:"
echo "   Server IP: **********"
echo "   Client IP range: ***********-***********0"
echo "   Port: 1701 (UDP)"
echo "   Authentication: HTTP plugin"
echo ""
echo "📁 Configuration files:"
echo "   /etc/xl2tpd/xl2tpd.conf"
echo "   /etc/ppp/options.xl2tpd"
echo "   /etc/xl2tpd/l2tp-secrets"
echo ""

print_header "Testing Instructions"

echo ""
echo "🧪 To test the fix:"
echo ""
echo "1. Check service status:"
echo "   sudo systemctl status xl2tpd"
echo ""
echo "2. Monitor logs:"
echo "   sudo journalctl -u xl2tpd -f"
echo ""
echo "3. Test authentication:"
echo "   test_auth test001 password"
echo ""
echo "4. Try L2TP connection from client"
echo ""
echo "5. Monitor connection logs:"
echo "   sudo tail -f /var/log/xl2tpd.log"
echo ""

if systemctl is-active xl2tpd >/dev/null 2>&1; then
    print_success "xl2tpd configuration fix completed successfully!"
    echo ""
    echo "The service is now running. You can test L2TP connections."
else
    print_error "Service is still not running. Check logs for additional issues."
    echo ""
    echo "Debug commands:"
    echo "  sudo journalctl -u xl2tpd --no-pager"
    echo "  sudo xl2tpd -D"
fi
