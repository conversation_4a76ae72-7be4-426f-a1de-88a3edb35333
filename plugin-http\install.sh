#!/bin/bash

# HTTP Authentication Plugin Installation Script
# Ubuntu 24.04 LTS
# Copyright 2024

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    print_error "This script needs to be run with sudo privileges"
    echo "Usage: sudo ./install.sh"
    exit 1
fi

print_header "HTTP Authentication Plugin Installation"
echo "System: $(lsb_release -d 2>/dev/null | cut -f2 || echo 'Ubuntu 24.04')"
echo "Date: $(date)"
echo ""

# Step 1: Install dependencies
print_header "Step 1: Installing Dependencies"
echo "Updating package list..."
apt-get update -qq

echo "Installing required packages..."
apt-get install -y \
    ppp-dev \
    libcurl4-openssl-dev \
    libjson-c-dev \
    libssl-dev \
    build-essential \
    pkg-config \
    xl2tpd

print_success "Dependencies installed"

# Step 2: Build plugin
print_header "Step 2: Building Plugin"
make clean
make

if [ -f "httpauth.so" ]; then
    print_success "Plugin built successfully"
    echo "Plugin size: $(ls -lh httpauth.so | awk '{print $5}')"
else
    print_error "Plugin build failed"
    exit 1
fi

# Step 3: Install plugin
print_header "Step 3: Installing Plugin"
make install

PPPD_VERSION=$(pppd --version 2>&1 | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+' | head -1 || echo "2.4.9")
PLUGIN_PATH="/usr/lib/pppd/$PPPD_VERSION/httpauth.so"

if [ -f "$PLUGIN_PATH" ]; then
    print_success "Plugin installed to $PLUGIN_PATH"
else
    print_error "Plugin installation failed"
    exit 1
fi

# Step 4: Build and install test program
print_header "Step 4: Installing Test Program"
make test

if [ -f "test_auth" ]; then
    cp test_auth /usr/local/bin/
    chmod 755 /usr/local/bin/test_auth
    print_success "Test program installed to /usr/local/bin/test_auth"
else
    print_warning "Test program build failed"
fi

# Step 5: Create configuration files
print_header "Step 5: Creating Configuration Files"

# Create xl2tpd options file
cat > /etc/ppp/options.xl2tpd << 'EOF'
# PPP options for xl2tpd with HTTP authentication
# /etc/ppp/options.xl2tpd

# Authentication
require-pap
refuse-chap
refuse-mschap
refuse-mschap-v2
refuse-eap

# HTTP Authentication Plugin
plugin /usr/lib/pppd/2.4.9/httpauth.so
auth-url https://testapi.softapi.cn/notify/pcm_ok
sign-key hYC0ztcOKp2aZ5t0

# IP authorization settings (fixes "Peer is not authorized" error)
noauth
ipcp-accept-local
ipcp-accept-remote

# Network settings
proxyarp
nobsdcomp
nodeflate
noipdefault

# Logging
logfile /var/log/xl2tpd.log

# IP settings
ms-dns *******
ms-dns *******

# Connection settings
lcp-echo-interval 30
lcp-echo-failure 4
idle 1800
EOF

# Update plugin path in options file
sed -i "s|/usr/lib/pppd/2.4.9/httpauth.so|$PLUGIN_PATH|g" /etc/ppp/options.xl2tpd

print_success "Created /etc/ppp/options.xl2tpd"

# Create corrected xl2tpd configuration
cat > /etc/xl2tpd/xl2tpd.conf << 'EOF'
; xl2tpd configuration for Ubuntu 24.04
; Compatible with xl2tpd version 1.3.18

[global]
listen-addr = 0.0.0.0
port = 1701
auth file = /etc/xl2tpd/l2tp-secrets

[lns default]
name = HTTPAuthVPNServer
ip range = **********0-**********00
local ip = **********
require chap = no
refuse pap = no
require authentication = yes
pppoptfile = /etc/ppp/options.xl2tpd
length bit = yes
challenge = no
exclusive = no
EOF

# Also create the example file
cp /etc/xl2tpd/xl2tpd.conf /etc/xl2tpd/xl2tpd.conf.example

print_success "Created /etc/xl2tpd/xl2tpd.conf"

# Create l2tp-secrets file
cat > /etc/xl2tpd/l2tp-secrets << 'EOF'
# L2TP secrets file for xl2tpd
# This file is required but authentication is handled by HTTP plugin
# Format: username server secret IP
* * * *
EOF

chmod 600 /etc/xl2tpd/l2tp-secrets
print_success "Created /etc/xl2tpd/l2tp-secrets"

# Note: Peer files are not needed for xl2tpd L2TP server operation
# xl2tpd automatically handles L2TP connections using /etc/ppp/options.xl2tpd

print_success "Skipped peer file creation (not needed for xl2tpd)"

# Step 6: Test installation
print_header "Step 6: Testing Installation"

echo "Testing plugin loading..."
# Use a more reliable test that doesn't hang
if timeout 3 bash -c "pppd plugin '$PLUGIN_PATH' nodetach 2>&1 | head -10" | grep -q "HTTP authentication plugin initialized"; then
    print_success "Plugin loads successfully"
else
    # Alternative test: check if plugin can be loaded without running pppd
    if ldd "$PLUGIN_PATH" >/dev/null 2>&1; then
        print_success "Plugin dependencies OK (loading test skipped)"
    else
        print_warning "Plugin loading test inconclusive"
    fi
fi

echo "Testing authentication API..."
if [ -f "/usr/local/bin/test_auth" ]; then
    if /usr/local/bin/test_auth testuser testpass >/dev/null 2>&1; then
        print_success "Authentication API test passed"
    else
        print_warning "Authentication API test failed (may be expected if server is unreachable)"
    fi
else
    print_warning "Test program not available"
fi

# Step 7: Display usage information
print_header "Installation Complete!"

echo ""
echo -e "${GREEN}HTTP Authentication Plugin successfully installed!${NC}"
echo ""
echo "📁 Files installed:"
echo "   Plugin: $PLUGIN_PATH"
echo "   Test program: /usr/local/bin/test_auth"
echo "   XL2TPD config: /etc/xl2tpd/xl2tpd.conf"
echo "   PPP options: /etc/ppp/options.xl2tpd"
echo "   L2TP secrets: /etc/xl2tpd/l2tp-secrets"
echo ""
echo "🚀 Quick start:"
echo "   1. Test authentication:"
echo "      test_auth <username> <password>"
echo ""
echo "   2. Start xl2tpd service:"
echo "      systemctl start xl2tpd"
echo "      systemctl enable xl2tpd"
echo ""
echo "   3. Test L2TP connection from client"
echo ""
echo "🔧 Configuration:"
echo "   - Edit /etc/ppp/options.xl2tpd for global PPP settings"
echo "   - Edit /etc/xl2tpd/xl2tpd.conf for L2TP settings"
echo "   - Modify auth-url and sign-key as needed"
echo ""
echo "📋 API format:"
echo "   URL: https://testapi.softapi.cn/notify/pcm_ok"
echo "   Method: POST"
echo "   Parameters: u (username), p (password)"
echo "   Response: {\"pass\":1,\"t\":timestamp,\"s\":\"signature\"}"
echo ""
echo "🔍 Troubleshooting:"
echo "   View logs: tail -f /var/log/xl2tpd.log"
echo "   Debug PPP: Add 'debug' to /etc/ppp/options.xl2tpd"
echo "   Test plugin: pppd plugin $PLUGIN_PATH nodetach debug"
echo ""

print_success "Installation completed successfully!"

echo ""
echo "Next steps:"
echo "1. Configure xl2tpd server settings"
echo "2. Test with: test_auth username password"
echo "3. Start xl2tpd service: systemctl start xl2tpd"
echo "4. Monitor logs for any issues"
