# Makefile for HTTP Authentication Plugin
# Ubuntu 24.04 LTS

CC = gcc
CFLAGS = -O2 -I/usr/include/pppd -fPIC -Wall -std=c99
CFLAGS += $(shell pkg-config --cflags libcurl json-c openssl)
CFLAGS += -Wno-unused-parameter

LDFLAGS = -shared
LIBS = $(shell pkg-config --libs libcurl json-c openssl)

# Get pppd version
PPPD_VERSION ?= $(shell pppd --version 2>&1 | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+' | head -1 || echo "2.4.9")
PLUGIN_DIR = /usr/lib/pppd/$(PPPD_VERSION)
MAN_DIR = /usr/share/man/man8

PLUGIN = httpauth.so

all: $(PLUGIN)

$(PLUGIN): httpauth.o
	@echo "Linking plugin..."
	$(CC) $(LDFLAGS) -o $(PLUGIN) httpauth.o $(LIBS)

httpauth.o: httpauth.c
	@echo "Compiling httpauth.c..."
	$(CC) $(CFLAGS) -c httpauth.c

test: test_auth

test_auth: test_auth.c
	@echo "Building test program..."
	$(CC) -o test_auth test_auth.c $(shell pkg-config --cflags --libs libcurl json-c openssl)

clean:
	rm -f *.o $(PLUGIN) test_auth

install: $(PLUGIN)
	@echo "Installing plugin to $(PLUGIN_DIR)..."
	install -d $(PLUGIN_DIR)
	install -m 755 $(PLUGIN) $(PLUGIN_DIR)/
	@echo "Installing man page..."
	install -d $(MAN_DIR)
	install -m 644 httpauth.8 $(MAN_DIR)/ || true
	@echo "Installation completed"
	@echo "Plugin installed: $(PLUGIN_DIR)/$(PLUGIN)"

uninstall:
	@echo "Removing plugin..."
	rm -f $(PLUGIN_DIR)/$(PLUGIN)
	rm -f $(MAN_DIR)/httpauth.8
	@echo "Plugin removed"

config:
	@echo "Configuration:"
	@echo "  CC: $(CC)"
	@echo "  CFLAGS: $(CFLAGS)"
	@echo "  LIBS: $(LIBS)"
	@echo "  PPPD_VERSION: $(PPPD_VERSION)"
	@echo "  PLUGIN_DIR: $(PLUGIN_DIR)"
	@echo "  MAN_DIR: $(MAN_DIR)"

.PHONY: all test clean install uninstall config
