#!/usr/bin/bash
# HTTP认证插件限速控制脚本
# 由pppd在IP连接建立时调用

# 获取脚本参数
INTERFACE="$1"
TTY="$2"
SPEED="$3"
CLIENT_IP="$4"
SERVER_IP="$5"
IPPARAM="$6"

# 获取环境变量
FILTER_ID="$HTTP_FILTER_ID"
FRAMED_ROUTE="$HTTP_FRAMED_ROUTE"
FRAMED_MTU="$HTTP_FRAMED_MTU"

# 日志函数
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') [HTTP-LIMITS] $1" >> /var/log/xl2tpd.log
}

# 记录连接信息和调试信息
log_message "=== 新连接开始 ==="
log_message "脚本参数: 接口=$INTERFACE, TTY=$TTY, 速度=$SPEED"
log_message "IP信息: 客户端=$CLIENT_IP, 服务器=$SERVER_IP, 参数=$IPPARAM"
log_message "环境变量: FILTER_ID=$FILTER_ID, FRAMED_ROUTE=$FRAMED_ROUTE, FRAMED_MTU=$FRAMED_MTU"

# 检查是否有过滤器ID
if [ -n "$FILTER_ID" ]; then
    log_message "检测到过滤器ID: $FILTER_ID"
    
    case "$FILTER_ID" in
        "basic")
            # 基础套餐: 1Mbps下行, 512Kbps上行
            log_message "开始应用基础套餐限速..."
            tc qdisc add dev $INTERFACE root handle 1: htb default 10 2>/dev/null
            tc class add dev $INTERFACE parent 1: classid 1:10 htb rate 1Mbit 2>/dev/null
            tc class add dev $INTERFACE parent 1: classid 1:20 htb rate 512Kbit 2>/dev/null
            tc filter add dev $INTERFACE protocol ip parent 1:0 prio 1 u32 match ip dst $CLIENT_IP flowid 1:10 2>/dev/null
            tc filter add dev $INTERFACE protocol ip parent 1:0 prio 1 u32 match ip src $CLIENT_IP flowid 1:20 2>/dev/null
            log_message "应用基础套餐限速: 用户 $CLIENT_IP (下载1Mbps/上传512Kbps) 接口 $INTERFACE"
            ;;
        "premium")
            # 高级套餐: 2Mbps下行, 2Mbps上行
            log_message "开始应用高级套餐限速..."
            tc qdisc add dev $INTERFACE root handle 1: htb default 10 2>/dev/null
            tc class add dev $INTERFACE parent 1: classid 1:10 htb rate 2Mbit 2>/dev/null
            tc class add dev $INTERFACE parent 1: classid 1:20 htb rate 2Mbit 2>/dev/null
            tc filter add dev $INTERFACE protocol ip parent 1:0 prio 1 u32 match ip dst $CLIENT_IP flowid 1:10 2>/dev/null
            tc filter add dev $INTERFACE protocol ip parent 1:0 prio 1 u32 match ip src $CLIENT_IP flowid 1:20 2>/dev/null
            log_message "应用高级套餐限速: 用户 $CLIENT_IP (下载2Mbps/上传2Mbps) 接口 $INTERFACE"
            ;;
        "enterprise")
            # 企业套餐: 10Mbps下行, 10Mbps上行
            log_message "开始应用企业套餐限速..."
            tc qdisc add dev $INTERFACE root handle 1: htb default 10 2>/dev/null
            tc class add dev $INTERFACE parent 1: classid 1:10 htb rate 10Mbit 2>/dev/null
            tc class add dev $INTERFACE parent 1: classid 1:20 htb rate 10Mbit 2>/dev/null
            tc filter add dev $INTERFACE protocol ip parent 1:0 prio 1 u32 match ip dst $CLIENT_IP flowid 1:10 2>/dev/null
            tc filter add dev $INTERFACE protocol ip parent 1:0 prio 1 u32 match ip src $CLIENT_IP flowid 1:20 2>/dev/null
            log_message "应用企业套餐限速: 用户 $CLIENT_IP (下载10Mbps/上传10Mbps) 接口 $INTERFACE"
            ;;
        "unlimited")
            # 不限速套餐
            log_message "用户 $CLIENT_IP 使用不限速套餐"
            ;;
        *)
            log_message "未知的过滤器ID: $FILTER_ID, 用户 $CLIENT_IP"
            ;;
    esac
else
    log_message "未设置过滤器ID (HTTP_FILTER_ID为空), 用户 $CLIENT_IP 不应用限速"
fi

# 处理自定义路由
if [ -n "$FRAMED_ROUTE" ]; then
    log_message "处理自定义路由: $FRAMED_ROUTE"
    # 格式: "网络/掩码 网关"
    read -r NETWORK GATEWAY <<< "$FRAMED_ROUTE"
    if ip route add $NETWORK via $GATEWAY 2>/dev/null; then
        log_message "添加路由成功: $NETWORK via $GATEWAY (用户 $CLIENT_IP)"
    else
        log_message "添加路由失败: $NETWORK via $GATEWAY (用户 $CLIENT_IP)"
    fi
fi

# 处理MTU设置
if [ -n "$FRAMED_MTU" ]; then
    log_message "处理MTU设置: $FRAMED_MTU"
    if ip link set dev $INTERFACE mtu $FRAMED_MTU 2>/dev/null; then
        log_message "设置MTU成功: $FRAMED_MTU (接口 $INTERFACE, 用户 $CLIENT_IP)"
    else
        log_message "设置MTU失败: $FRAMED_MTU (接口 $INTERFACE, 用户 $CLIENT_IP)"
    fi
fi

# 显示当前tc配置
log_message "当前tc配置:"
tc -s qdisc show dev $INTERFACE >> /var/log/xl2tpd.log 2>&1
tc -s class show dev $INTERFACE >> /var/log/xl2tpd.log 2>&1

# 记录完成信息
log_message "限速配置完成: 用户 $CLIENT_IP"
log_message "=== 连接配置结束 ==="
