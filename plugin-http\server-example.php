<?php
/**
 * HTTP Authentication Server Example for PPP/L2TP
 * 
 * This script handles authentication requests from the HTTP auth plugin
 * Parameters: u (username), p (password)
 * Response: JSON with pass, t (timestamp), s (signature)
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Configuration
$sign_key = 'hYC0ztcOKp2aZ5t0';
$debug_mode = true; // Set to false in production

// Log function
function debug_log($message) {
    global $debug_mode;
    if ($debug_mode) {
        error_log("[HTTP Auth] " . $message);
    }
}

// Get POST parameters
$username = $_POST['u'] ?? '';
$password = $_POST['p'] ?? '';

debug_log("Authentication request for user: $username from IP: " . $_SERVER['REMOTE_ADDR']);

// Validate input
if (empty($username) || empty($password)) {
    debug_log("Missing username or password");
    echo json_encode([
        'pass' => 0,
        't' => time(),
        's' => md5('error' . time() . $sign_key),
        'error' => 'Missing username or password'
    ]);
    exit;
}

// Example user database - replace with your authentication system
$valid_users = [
    'testuser' => 'testpass',
    'admin' => 'admin123',
    'user1' => 'password1',
    'user2' => 'password2',
    'demo' => 'demo',
    'vpnuser' => 'vpnpass123'
];

// Authentication function
function authenticate_user($username, $password) {
    global $valid_users;
    
    // Simple example - replace with your authentication logic
    // This could connect to:
    // - MySQL/PostgreSQL database
    // - LDAP server
    // - External API
    // - File-based user store
    
    return isset($valid_users[$username]) && $valid_users[$username] === $password;
}

// Perform authentication
$authenticated = authenticate_user($username, $password);

// Generate timestamp
$timestamp = time();

// Calculate signature: md5(username + password + timestamp + sign_key)
$signature_input = $username . $password . $timestamp . $sign_key;
$signature = md5($signature_input);

debug_log("Signature input: $signature_input");
debug_log("Calculated signature: $signature");
debug_log("Authentication result: " . ($authenticated ? 'SUCCESS' : 'FAILED'));

// Prepare response
$response = [
    'pass' => $authenticated ? 1 : 0,
    't' => $timestamp,
    's' => $signature
];

// Add debug information if enabled
if ($debug_mode) {
    $response['debug'] = [
        'username' => $username,
        'timestamp' => $timestamp,
        'signature_input' => $signature_input,
        'auth_result' => $authenticated ? 'success' : 'failed',
        'server_time' => date('Y-m-d H:i:s', $timestamp),
        'client_ip' => $_SERVER['REMOTE_ADDR']
    ];
}

// Log the response
debug_log("Response sent: " . json_encode($response));

// Return JSON response
echo json_encode($response);

// Optional: Log to file for monitoring
if ($debug_mode) {
    $log_entry = date('Y-m-d H:i:s') . " - User: $username, IP: " . $_SERVER['REMOTE_ADDR'] . 
                 ", Result: " . ($authenticated ? 'SUCCESS' : 'FAILED') . "\n";
    file_put_contents('/tmp/http_auth.log', $log_entry, FILE_APPEND | LOCK_EX);
}
?>
