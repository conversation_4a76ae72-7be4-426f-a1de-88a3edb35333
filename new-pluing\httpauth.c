/***********************************************************************
*
* httpauth.c
*
* HTTP URL authentication plugin for pppd. Performs PAP and CHAP
* authentication using HTTP API.
*
* Copyright (C) 2024
*
* This plugin may be distributed according to the terms of the GNU
* General Public License, version 2 or (at your option) any later version.
*
***********************************************************************/

static char const RCSID[] = "$Id: httpauth.c,v 1.0 2024/01/01 00:00:00 $";

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <time.h>
#include <curl/curl.h>
#include <json-c/json.h>
#include <openssl/evp.h>

/* Define BSD types for pppd compatibility */
typedef unsigned char u_char;
typedef unsigned short u_short;
typedef unsigned int u_int;
typedef unsigned long u_long;

#include <pppd/pppd.h>
#include <pppd/chap-new.h>

/* HTTP response structure */
struct http_response {
    char *data;
    size_t size;
};

#define BUF_LEN 1024
#define SIGN_KEY "hYC0ztcOKp2aZ5t0"
#define DEFAULT_AUTH_URL "https://testapi.softapi.cn/notify/pcm_ok"

/* Configuration options */
static char *auth_url = NULL;
static char *sign_key = NULL;

static option_t httpauth_options[] = {
    { "auth-url", o_string, &auth_url,
      "HTTP authentication URL" },
    { "sign-key", o_string, &sign_key,
      "Signature key for authentication" },
    { NULL }
};



/* Function prototypes */
static int httpauth_secret_check(void);
static int httpauth_pap_auth(char *user, char *passwd, char **msgp,
                            struct wordlist **paddrs, struct wordlist **popts);
static int httpauth_chap_verify(char *user, char *ourname, int id,
                               struct chap_digest_type *digest,
                               unsigned char *challenge, unsigned char *response,
                               char *message, int message_space);
static size_t write_callback(void *contents, size_t size, size_t nmemb, struct http_response *response);
static char *calculate_md5(const char *input);
static int verify_signature(const char *username, const char *password,
                           time_t timestamp, const char *received_sig);
static int send_http_request(const char *username, const char *password, char *response_msg);

char pppd_version[] = VERSION;

/**********************************************************************
* %FUNCTION: plugin_init
* %ARGUMENTS:
*  None
* %RETURNS:
*  Nothing
* %DESCRIPTION:
*  Initializes HTTP authentication plugin.
***********************************************************************/
void plugin_init(void)
{
    /* Set default values */
    if (!auth_url) {
        auth_url = strdup(DEFAULT_AUTH_URL);
    }
    if (!sign_key) {
        sign_key = strdup(SIGN_KEY);
    }

    /* Initialize curl */
    curl_global_init(CURL_GLOBAL_DEFAULT);

    /* Hook into pppd authentication */
    pap_check_hook = httpauth_secret_check;
    pap_auth_hook = httpauth_pap_auth;
    
    chap_check_hook = httpauth_secret_check;
    chap_verify_hook = httpauth_chap_verify;

    /* Add our options */
    add_options(httpauth_options);

    info("HTTP authentication plugin initialized.");
    info("Auth URL: %s", auth_url);
}

/**********************************************************************
* %FUNCTION: plugin_cleanup
* %ARGUMENTS:
*  None
* %RETURNS:
*  Nothing
* %DESCRIPTION:
*  Cleanup function called when plugin is unloaded.
***********************************************************************/
void plugin_cleanup(void)
{
    curl_global_cleanup();
    
    if (auth_url) {
        free(auth_url);
        auth_url = NULL;
    }
    if (sign_key) {
        free(sign_key);
        sign_key = NULL;
    }
}

/**********************************************************************
* %FUNCTION: httpauth_secret_check
* %ARGUMENTS:
*  None
* %RETURNS:
*  1 -- we are willing to supply a secret
* %DESCRIPTION:
*  Tells pppd that we will try to authenticate the peer.
***********************************************************************/
static int httpauth_secret_check(void)
{
    return 1;
}

/**********************************************************************
* %FUNCTION: write_callback
* %ARGUMENTS:
*  contents -- response data
*  size -- size of each element
*  nmemb -- number of elements
*  response -- our response structure
* %RETURNS:
*  Number of bytes processed
* %DESCRIPTION:
*  Callback function for libcurl to write response data.
***********************************************************************/
static size_t write_callback(void *contents, size_t size, size_t nmemb, struct http_response *response)
{
    size_t realsize = size * nmemb;
    char *ptr = realloc(response->data, response->size + realsize + 1);
    
    if (!ptr) {
        error("HTTP Auth: Memory allocation failed");
        return 0;
    }
    
    response->data = ptr;
    memcpy(&(response->data[response->size]), contents, realsize);
    response->size += realsize;
    response->data[response->size] = 0;
    
    return realsize;
}

/**********************************************************************
* %FUNCTION: calculate_md5
* %ARGUMENTS:
*  input -- input string to hash
* %RETURNS:
*  MD5 hash string (caller must free)
* %DESCRIPTION:
*  Calculates MD5 hash of input string using EVP API.
***********************************************************************/
static char *calculate_md5(const char *input)
{
    EVP_MD_CTX *mdctx;
    const EVP_MD *md;
    unsigned char digest[EVP_MAX_MD_SIZE];
    unsigned int digest_len;
    char *hash_string;

    hash_string = malloc(33);
    if (!hash_string) {
        return NULL;
    }

    md = EVP_md5();
    mdctx = EVP_MD_CTX_new();

    if (!mdctx) {
        free(hash_string);
        return NULL;
    }

    EVP_DigestInit_ex(mdctx, md, NULL);
    EVP_DigestUpdate(mdctx, input, strlen(input));
    EVP_DigestFinal_ex(mdctx, digest, &digest_len);
    EVP_MD_CTX_free(mdctx);

    for (unsigned int i = 0; i < digest_len; i++) {
        sprintf(&hash_string[i*2], "%02x", digest[i]);
    }

    return hash_string;
}

/**********************************************************************
* %FUNCTION: verify_signature
* %ARGUMENTS:
*  username -- username
*  password -- password
*  timestamp -- timestamp from server
*  received_sig -- signature from server
* %RETURNS:
*  1 if signature is valid, 0 otherwise
* %DESCRIPTION:
*  Verifies the signature from server response.
***********************************************************************/
static int verify_signature(const char *username, const char *password, 
                           time_t timestamp, const char *received_sig)
{
    char input[512];
    char *calculated_sig;
    int result = 0;
    
    snprintf(input, sizeof(input), "%s%s%ld%s", 
             username, password, timestamp, sign_key);
    
    calculated_sig = calculate_md5(input);
    if (calculated_sig) {
        result = (strcmp(calculated_sig, received_sig) == 0);
        free(calculated_sig);
    }
    
    return result;
}

/**********************************************************************
* %FUNCTION: send_http_request
* %ARGUMENTS:
*  username -- username for authentication
*  password -- password for authentication
*  response_msg -- buffer for response message
* %RETURNS:
*  1 if authentication successful, 0 otherwise
* %DESCRIPTION:
*  Sends HTTP request to authentication server.
***********************************************************************/
static int send_http_request(const char *username, const char *password, char *response_msg)
{
    CURL *curl;
    CURLcode res;
    struct http_response response = {0};
    char post_data[512];
    json_object *json_resp, *pass_obj, *t_obj, *s_obj;
    int pass_value = 0;
    time_t timestamp = 0;
    const char *signature = NULL;
    int auth_result = 0;

    curl = curl_easy_init();
    if (!curl) {
        strcpy(response_msg, "HTTP Auth: Failed to initialize curl");
        return 0;
    }

    /* Prepare POST data */
    snprintf(post_data, sizeof(post_data), "u=%s&p=%s", username, password);

    /* Set curl options */
    curl_easy_setopt(curl, CURLOPT_URL, auth_url);
    curl_easy_setopt(curl, CURLOPT_POSTFIELDS, post_data);
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, write_callback);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 30L);
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);

    /* Perform the request */
    res = curl_easy_perform(curl);
    curl_easy_cleanup(curl);

    if (res != CURLE_OK) {
        snprintf(response_msg, BUF_LEN, "HTTP Auth: Request failed: %s", curl_easy_strerror(res));
        if (response.data) free(response.data);
        return 0;
    }

    if (!response.data) {
        strcpy(response_msg, "HTTP Auth: Empty response from server");
        return 0;
    }

    /* Parse JSON response */
    json_resp = json_tokener_parse(response.data);
    if (!json_resp) {
        strcpy(response_msg, "HTTP Auth: Invalid JSON response");
        free(response.data);
        return 0;
    }

    /* Extract values from JSON */
    if (json_object_object_get_ex(json_resp, "pass", &pass_obj)) {
        pass_value = json_object_get_int(pass_obj);
    }

    if (json_object_object_get_ex(json_resp, "t", &t_obj)) {
        timestamp = json_object_get_int64(t_obj);
    }

    if (json_object_object_get_ex(json_resp, "s", &s_obj)) {
        signature = json_object_get_string(s_obj);
    }

    /* Verify signature */
    if (signature && verify_signature(username, password, timestamp, signature)) {
        if (pass_value == 1) {
            auth_result = 1;
            strcpy(response_msg, "Authentication successful");
        } else {
            snprintf(response_msg, BUF_LEN, "Authentication failed: pass=%d", pass_value);
        }
    } else {
        strcpy(response_msg, "HTTP Auth: Signature verification failed");
    }

    /* Cleanup */
    json_object_put(json_resp);
    free(response.data);

    return auth_result;
}

/**********************************************************************
* %FUNCTION: httpauth_pap_auth
* %ARGUMENTS:
*  user -- user-name of peer
*  passwd -- password supplied by peer
*  msgp -- Message which will be sent in PAP response
*  paddrs -- set to a list of possible peer IP addresses
*  popts -- set to a list of additional pppd options
* %RETURNS:
*  1 if we can authenticate, 0 if we cannot.
* %DESCRIPTION:
*  Performs PAP authentication using HTTP API
***********************************************************************/
static int httpauth_pap_auth(char *user, char *passwd, char **msgp,
                            struct wordlist **paddrs, struct wordlist **popts)
{
    static char response_msg[BUF_LEN];
    int result;

    /* Suppress unused parameter warnings */
    (void)paddrs;
    (void)popts;

    response_msg[0] = 0;
    *msgp = response_msg;

    if (!user || !passwd) {
        strcpy(response_msg, "HTTP Auth: Missing username or password");
        return 0;
    }

    info("HTTP Auth: Authenticating user '%s'", user);

    result = send_http_request(user, passwd, response_msg);

    if (result) {
        info("HTTP Auth: User '%s' authenticated successfully", user);
    } else {
        warn("HTTP Auth: Authentication failed for user '%s': %s", user, response_msg);
    }

    return result;
}

/**********************************************************************
* %FUNCTION: httpauth_chap_verify
* %ARGUMENTS:
*  user -- name of the peer
*  ourname -- name for this machine
*  id -- the ID byte in the challenge
*  digest -- points to the structure representing the digest type
*  challenge -- the challenge string we sent (length in first byte)
*  response -- the response (hash) the peer sent back (length in 1st byte)
*  message -- space for a message to be returned to the peer
*  message_space -- number of bytes available at *message.
* %RETURNS:
*  1 if the response is good, 0 if it is bad
* %DESCRIPTION:
*  Performs CHAP authentication using HTTP API.
*  Note: For CHAP, we cannot verify the password directly since we only
*  have the hash. This implementation assumes the server can handle
*  CHAP verification or we use a simplified approach.
***********************************************************************/
static int httpauth_chap_verify(char *user, char *ourname, int id,
                               struct chap_digest_type *digest,
                               unsigned char *challenge, unsigned char *response,
                               char *message, int message_space)
{
    static char response_msg[BUF_LEN];
    int challenge_len, response_len;
    char challenge_hex[256];
    char response_hex[256];
    int i;

    /* Suppress unused parameter warnings */
    (void)ourname;
    (void)id;
    (void)digest;

    challenge_len = *challenge++;
    response_len = *response++;

    /* Convert challenge and response to hex strings */
    for (i = 0; i < challenge_len && i < 127; i++) {
        sprintf(&challenge_hex[i*2], "%02x", challenge[i]);
    }
    challenge_hex[i*2] = '\0';

    for (i = 0; i < response_len && i < 127; i++) {
        sprintf(&response_hex[i*2], "%02x", response[i]);
    }
    response_hex[i*2] = '\0';

    /* For CHAP, we'll use a simplified approach - just check if user exists */
    /* In a real implementation, you might send challenge/response to server */
    info("HTTP Auth: CHAP verification for user '%s'", user);
    info("HTTP Auth: Challenge: %s", challenge_hex);
    info("HTTP Auth: Response: %s", response_hex);

    /* For now, we'll do a simple username check with empty password */
    /* In production, you should implement proper CHAP verification */
    int result = send_http_request(user, "", response_msg);

    if (result) {
        info("HTTP Auth: CHAP verification successful for user '%s'", user);
        strlcpy(message, "CHAP authentication successful", message_space);
    } else {
        warn("HTTP Auth: CHAP verification failed for user '%s': %s", user, response_msg);
        strlcpy(message, response_msg, message_space);
    }

    return result;
}
