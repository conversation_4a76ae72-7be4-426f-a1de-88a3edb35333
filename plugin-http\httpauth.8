.\" Manual page for httpauth plugin
.TH HTTPAUTH 8 "1 Jan 2024"
.SH NAME
httpauth \- HTTP URL authentication plugin for pppd
.SH SYNOPSIS
.B pppd
[
.I options
]
.B plugin httpauth.so
[
.I httpauth-options
]
.SH DESCRIPTION
The
.B httpauth
plugin for
.BR pppd (8)
performs authentication of peers via HTTP API calls instead of using
local password files or RADIUS servers. This plugin is particularly
designed for use with xl2tpd L2TP VPN servers.

When a peer attempts to authenticate, the plugin sends an HTTP POST request
to the configured authentication URL with the username and password parameters
(sent as 'u' and 'p' respectively). The server response is expected to be in 
JSON format containing authentication result and signature verification data.

.SH OPTIONS
The
.B httpauth
plugin supports the following options:

.TP
.BI "auth-url " url
Specifies the HTTP URL to use for authentication requests.
The default is "https://testapi.softapi.cn/notify/pcm_ok".

.TP
.BI "sign-key " key
Specifies the signature key used for response verification.
The default is "hYC0ztcOKp2aZ5t0".

.SH AUTHENTICATION PROTOCOL
The plugin sends HTTP POST requests with the following parameters:
.IP \(bu 4
.B u
- The username provided by the peer
.IP \(bu 4
.B p
- The password provided by the peer

The server is expected to respond with JSON in the following format:
.nf
{
    "pass": 1,
    "t": 1752454664,
    "s": "673261d844d929b52a468404aac290ca"
}
.fi

Where:
.IP \(bu 4
.B pass
- Authentication result (1 = success, other values = failure)
.IP \(bu 4
.B t
- Server timestamp
.IP \(bu 4
.B s
- MD5 signature calculated as md5(username + password + t + sign_key)

.SH SIGNATURE VERIFICATION
The plugin verifies the server response by calculating the expected signature
using the formula:
.nf
md5(username + password + timestamp + sign_key)
.fi

If the calculated signature matches the one provided by the server, and the
pass value is 1, authentication succeeds.

.SH EXAMPLES
To use the httpauth plugin with xl2tpd:
.nf
# In /etc/ppp/options.xl2tpd
plugin httpauth.so
auth-url https://testapi.softapi.cn/notify/pcm_ok
sign-key hYC0ztcOKp2aZ5t0
.fi

To specify a custom authentication URL:
.nf
plugin httpauth.so auth-url https://myserver.com/auth
.fi

To specify a custom signature key:
.nf
plugin httpauth.so sign-key mySecretKey123
.fi

.SH XL2TPD INTEGRATION
This plugin is designed to work with xl2tpd. Example configuration:

.B /etc/xl2tpd/xl2tpd.conf:
.nf
[global]
listen-addr = 0.0.0.0
port = 1701

[lns default]
ip range = ***************-***************
local ip = *************
require authentication = yes
pppoptfile = /etc/ppp/options.xl2tpd
.fi

.B /etc/ppp/options.xl2tpd:
.nf
require-pap
plugin httpauth.so
auth-url https://testapi.softapi.cn/notify/pcm_ok
sign-key hYC0ztcOKp2aZ5t0
proxyarp
ms-dns *******
.fi

.SH DEPENDENCIES
This plugin requires the following libraries:
.IP \(bu 4
libcurl - for HTTP requests
.IP \(bu 4
libjson-c - for JSON parsing
.IP \(bu 4
libssl/libcrypto - for MD5 calculation

.SH FILES
.TP
.I /usr/lib/pppd/*/httpauth.so
The plugin shared library
.TP
.I /etc/ppp/options.xl2tpd
PPP options file for xl2tpd
.TP
.I /etc/xl2tpd/xl2tpd.conf
xl2tpd configuration file

.SH DIAGNOSTICS
The plugin logs authentication attempts and results to the system log.
Enable debug mode in pppd to see detailed authentication information:
.nf
debug
logfile /var/log/ppp-debug.log
.fi

.SH SECURITY CONSIDERATIONS
.IP \(bu 4
Always use HTTPS for authentication URLs in production
.IP \(bu 4
Keep the signature key secret and rotate it regularly
.IP \(bu 4
Monitor authentication logs for security incidents
.IP \(bu 4
Consider implementing rate limiting on the authentication server
.IP \(bu 4
Use IPSec with L2TP for additional security

.SH SEE ALSO
.BR pppd (8),
.BR xl2tpd (8),
.BR xl2tpd.conf (5)

.SH AUTHOR
HTTP Authentication Plugin for pppd

.SH BUGS
Report bugs to the system administrator or plugin maintainer.

.SH NOTES
This plugin only supports PAP authentication. CHAP, MS-CHAP, and other
authentication methods are not supported and should be disabled in the
PPP configuration.
