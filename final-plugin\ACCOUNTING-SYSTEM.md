# HTTP认证插件 - 计费系统说明

## 概述

HTTP认证插件现在集成了完整的计费系统，参考RADIUS插件的实现，提供基于时间和流量的精确计费功能。

## 计费系统特性

### 核心功能
- ✅ **会话开始计费** - 连接建立时自动发送
- ✅ **中间计费** - 定期发送当前会话统计
- ✅ **会话停止计费** - 连接断开时自动发送
- ✅ **时间计费** - 精确到秒的会话时间统计
- ✅ **流量计费** - 上传/下载字节数和包数统计
- ✅ **会话管理** - 唯一会话ID和状态跟踪

### 参考RADIUS标准
- 基于RADIUS Accounting的设计理念
- 兼容标准的计费属性
- 支持Start/Interim-Update/Stop计费类型

## 配置选项

### PPP配置文件 (/etc/ppp/options.xl2tpd)
```
# 计费系统配置
acct-url https://testapi.softapi.cn/notify/pcm_acct
do-accounting true
acct-interim-interval 300
```

### 配置参数说明
- `acct-url`: 计费服务器URL
- `do-accounting`: 是否启用计费功能 (true/false)
- `acct-interim-interval`: 中间计费间隔(秒)，0表示禁用中间计费

## API接口格式

### 计费请求格式
```
POST https://testapi.softapi.cn/notify/pcm_acct
Content-Type: application/x-www-form-urlencoded

acct_type=Start&u=用户名&session_id=会话ID&session_time=会话时间&
bytes_in=输入字节&bytes_out=输出字节&pkts_in=输入包数&pkts_out=输出包数&
t=时间戳&s=MD5签名
```

### 参数说明
- `acct_type`: 计费类型 (Start/Interim-Update/Stop)
- `u`: 用户名
- `session_id`: 唯一会话标识符 (32字符)
- `session_time`: 会话持续时间(秒)
- `bytes_in`: 接收字节数
- `bytes_out`: 发送字节数
- `pkts_in`: 接收包数
- `pkts_out`: 发送包数
- `t`: Unix时间戳
- `s`: MD5签名

### 签名计算
```
MD5(acct_type + username + session_id + session_time + timestamp + 
    bytes_in + bytes_out + pkts_in + pkts_out + timestamp + sign_key)
```

### 服务器响应格式
```json
{
    "status": "ok"
}
```

## 计费流程

### 1. 会话开始 (Start)
- **触发时机**: IP连接建立时
- **数据内容**: 基本会话信息，流量为0
- **示例**:
```
acct_type=Start&u=test001&session_id=12345678901234567890123456789012&
session_time=0&bytes_in=0&bytes_out=0&pkts_in=0&pkts_out=0&
t=1752454664&s=abc123...
```

### 2. 中间计费 (Interim-Update)
- **触发时机**: 定期发送 (默认5分钟)
- **数据内容**: 当前会话统计信息
- **示例**:
```
acct_type=Interim-Update&u=test001&session_id=12345678901234567890123456789012&
session_time=300&bytes_in=1024000&bytes_out=512000&pkts_in=1000&pkts_out=800&
t=1752454964&s=def456...
```

### 3. 会话停止 (Stop)
- **触发时机**: IP连接断开时
- **数据内容**: 完整会话统计信息
- **示例**:
```
acct_type=Stop&u=test001&session_id=12345678901234567890123456789012&
session_time=1800&bytes_in=5120000&bytes_out=2560000&pkts_in=5000&pkts_out=4000&
t=1752456464&s=ghi789...
```

## 服务器端实现示例

### PHP示例
```php
<?php
header('Content-Type: application/json');

$acct_type = $_POST['acct_type'] ?? '';
$username = $_POST['u'] ?? '';
$session_id = $_POST['session_id'] ?? '';
$session_time = intval($_POST['session_time'] ?? 0);
$bytes_in = intval($_POST['bytes_in'] ?? 0);
$bytes_out = intval($_POST['bytes_out'] ?? 0);
$pkts_in = intval($_POST['pkts_in'] ?? 0);
$pkts_out = intval($_POST['pkts_out'] ?? 0);
$timestamp = intval($_POST['t'] ?? 0);
$signature = $_POST['s'] ?? '';

$sign_key = 'hYC0ztcOKp2aZ5t0';

// 验证签名
$expected_sig = md5($acct_type . $username . $session_id . $session_time . 
                   $timestamp . $bytes_in . $bytes_out . $pkts_in . $pkts_out . 
                   $timestamp . $sign_key);

if ($signature !== $expected_sig) {
    echo json_encode(['status' => 'error', 'message' => 'Invalid signature']);
    exit;
}

// 处理计费数据
switch ($acct_type) {
    case 'Start':
        // 记录会话开始
        log_session_start($username, $session_id, $timestamp);
        break;
        
    case 'Interim-Update':
        // 更新会话统计
        update_session_stats($session_id, $session_time, $bytes_in, $bytes_out, 
                           $pkts_in, $pkts_out);
        break;
        
    case 'Stop':
        // 记录会话结束
        log_session_stop($session_id, $session_time, $bytes_in, $bytes_out, 
                        $pkts_in, $pkts_out);
        break;
}

echo json_encode(['status' => 'ok']);
?>
```

### Python Flask示例
```python
from flask import Flask, request, jsonify
import hashlib
import time

app = Flask(__name__)
SIGN_KEY = 'hYC0ztcOKp2aZ5t0'

@app.route('/notify/pcm_acct', methods=['POST'])
def accounting():
    acct_type = request.form.get('acct_type', '')
    username = request.form.get('u', '')
    session_id = request.form.get('session_id', '')
    session_time = int(request.form.get('session_time', 0))
    bytes_in = int(request.form.get('bytes_in', 0))
    bytes_out = int(request.form.get('bytes_out', 0))
    pkts_in = int(request.form.get('pkts_in', 0))
    pkts_out = int(request.form.get('pkts_out', 0))
    timestamp = int(request.form.get('t', 0))
    signature = request.form.get('s', '')
    
    # 验证签名
    sign_input = f"{acct_type}{username}{session_id}{session_time}{timestamp}{bytes_in}{bytes_out}{pkts_in}{pkts_out}{timestamp}{SIGN_KEY}"
    expected_sig = hashlib.md5(sign_input.encode()).hexdigest()
    
    if signature != expected_sig:
        return jsonify({'status': 'error', 'message': 'Invalid signature'})
    
    # 处理计费数据
    if acct_type == 'Start':
        # 记录会话开始
        log_session_start(username, session_id, timestamp)
    elif acct_type == 'Interim-Update':
        # 更新会话统计
        update_session_stats(session_id, session_time, bytes_in, bytes_out, pkts_in, pkts_out)
    elif acct_type == 'Stop':
        # 记录会话结束
        log_session_stop(session_id, session_time, bytes_in, bytes_out, pkts_in, pkts_out)
    
    return jsonify({'status': 'ok'})
```

## 测试方法

### 1. 编译测试程序
```bash
cd final-plugin
make test
```

### 2. 测试计费API
```bash
# 测试计费功能
./test_accounting test001

# 测试特定用户
./test_accounting myuser
```

### 3. 实际连接测试
```bash
# 监控日志
sudo tail -f /var/log/xl2tpd.log

# 从客户端连接L2TP VPN
# 观察计费日志输出
```

## 预期日志输出

### 连接建立时
```
HTTP认证: 用户 'test001' 认证成功
HTTP计费: 用户 'test001' 开始计费，会话ID: 12345678901234567890123456789012
```

### 中间计费时
```
HTTP计费: 用户 'test001' 中间计费，会话时间: 300秒，流量: 1024000/512000字节
```

### 连接断开时
```
HTTP计费: 用户 'test001' 停止计费，会话时间: 1800秒，流量: 5120000/2560000字节
```

## 故障排除

### 常见问题
1. **计费请求失败** - 检查acct-url配置和网络连接
2. **签名验证失败** - 确认sign-key配置正确
3. **中间计费不工作** - 检查acct-interim-interval设置

### 调试方法
1. 启用debug模式查看详细日志
2. 使用test_accounting程序测试API
3. 检查服务器端日志确认请求到达

## 总结

集成的计费系统提供了完整的会话管理和流量统计功能，与RADIUS标准兼容，为VPN服务提供了精确的计费能力。通过HTTP API的方式，可以轻松集成到现有的计费系统中。
