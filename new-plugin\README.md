# HTTP Authentication Plugin for pppd

This plugin provides HTTP URL-based authentication for PPP connections, allowing you to authenticate users against a web service instead of local files or RADIUS servers.

**Optimized for Ubuntu 24.04 LTS**

> 📋 **Quick Start**: See [QUICKSTART-Ubuntu24.04.md](QUICKSTART-Ubuntu24.04.md) for a step-by-step setup guide.

## Features

- **PAP Authentication**: Full support for Password Authentication Protocol
- **CHAP Authentication**: Basic support for Challenge Handshake Authentication Protocol
- **Signature Verification**: MD5-based signature verification for security
- **JSON Response Parsing**: Handles JSON responses from authentication server
- **Configurable URLs**: Support for custom authentication endpoints
- **SSL/TLS Support**: HTTPS requests with SSL verification options

## Requirements

### System Dependencies
- libcurl4-openssl-dev (for HTTP requests)
- libjson-c-dev (for JSON parsing)
- libssl-dev (for MD5 calculations)
- pppd development headers

### Installation on Ubuntu 24.04 (Recommended)
```bash
# Install all dependencies automatically
sudo ./install-deps.sh
```

### Manual Installation on Ubuntu/Debian
```bash
sudo apt-get update
sudo apt-get install ppp-dev libcurl4-openssl-dev libjson-c-dev libssl-dev build-essential pkg-config
```

### Installation on CentOS/RHEL
```bash
sudo yum install ppp-devel libcurl-devel json-c-devel openssl-devel gcc make pkg-config
```

## Compilation

### Ubuntu 24.04 (Recommended)
```bash
cd new-plugin
# Install dependencies
sudo ./install-deps.sh
# Build plugin
./build.sh
# Install plugin
sudo make install
```

### Manual Compilation
```bash
cd new-plugin
make config  # Show configuration
make         # Build plugin
make test    # Build test program
sudo make install
```

## Configuration

### Basic Usage
```bash
pppd plugin httpauth.so
```

### Custom Authentication URL
```bash
pppd plugin httpauth.so auth-url https://your-server.com/auth
```

### Custom Signature Key
```bash
pppd plugin httpauth.so sign-key yourSecretKey123
```

### Complete Example
```bash
pppd plugin httpauth.so \
    auth-url https://api.example.com/ppp/auth \
    sign-key myCustomSignKey456
```

## API Protocol

### Request Format
The plugin sends HTTP POST requests with the following parameters:
- `username`: The username provided by the PPP client
- `password`: The password provided by the PPP client

### Response Format
The server must respond with JSON in the following format:
```json
{
    "pass": 1,
    "t": 1752454664,
    "s": "673261d844d929b52a468404aac290ca"
}
```

Where:
- `pass`: Authentication result (1 = success, other values = failure)
- `t`: Server timestamp (Unix timestamp)
- `s`: MD5 signature for verification

### Signature Calculation
The signature is calculated as:
```
md5(username + password + timestamp + sign_key)
```

Example in PHP:
```php
$signature = md5($username . $password . $timestamp . $sign_key);
```

## Server Implementation Example

### PHP Example
```php
<?php
header('Content-Type: application/json');

$username = $_POST['username'] ?? '';
$password = $_POST['password'] ?? '';
$sign_key = 'hYC0ztcOKp2aZ5t0';

// Your authentication logic here
$authenticated = authenticate_user($username, $password);

$timestamp = time();
$signature = md5($username . $password . $timestamp . $sign_key);

$response = [
    'pass' => $authenticated ? 1 : 0,
    't' => $timestamp,
    's' => $signature
];

echo json_encode($response);
?>
```

### Python Example
```python
import hashlib
import json
import time
from flask import Flask, request, jsonify

app = Flask(__name__)
SIGN_KEY = 'hYC0ztcOKp2aZ5t0'

@app.route('/auth', methods=['POST'])
def authenticate():
    username = request.form.get('username', '')
    password = request.form.get('password', '')
    
    # Your authentication logic here
    authenticated = check_credentials(username, password)
    
    timestamp = int(time.time())
    signature_input = f"{username}{password}{timestamp}{SIGN_KEY}"
    signature = hashlib.md5(signature_input.encode()).hexdigest()
    
    return jsonify({
        'pass': 1 if authenticated else 0,
        't': timestamp,
        's': signature
    })
```

## Security Considerations

1. **HTTPS Only**: Always use HTTPS for authentication requests in production
2. **Signature Verification**: The plugin verifies server responses using MD5 signatures
3. **Key Management**: Keep your signature key secret and rotate it regularly
4. **Timeout Settings**: Configure appropriate timeout values for HTTP requests
5. **SSL Verification**: Enable SSL certificate verification in production

## Troubleshooting

### Common Issues

1. **Plugin not loading**
   - Check if all dependencies are installed
   - Verify plugin path is correct
   - Check pppd logs for error messages

2. **Authentication failures**
   - Verify the authentication URL is accessible
   - Check server response format
   - Verify signature calculation on server side

3. **SSL/TLS errors**
   - Check certificate validity
   - Consider disabling SSL verification for testing (not recommended for production)

### Debug Mode
Enable pppd debug mode to see detailed authentication logs:
```bash
pppd debug plugin httpauth.so
```

### Log Files
Check system logs for authentication attempts:
```bash
tail -f /var/log/syslog | grep pppd
```

## Limitations

1. **CHAP Implementation**: Current CHAP support is simplified and may not work with all clients
2. **Error Handling**: Limited retry mechanisms for failed HTTP requests
3. **Caching**: No caching of authentication results

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This plugin is distributed under the GNU General Public License, version 2 or later.

## Support

For issues and questions, please check the troubleshooting section above or consult the pppd documentation.
