#!/bin/bash

# Test compilation script for debugging
# Copyright 2024

set -e

echo "=== Testing Compilation ==="

# Clean first
echo "Cleaning..."
make clean

# Show configuration
echo "Configuration:"
make config

echo ""
echo "=== Compiling httpauth.c ==="

# Try to compile just the object file first
echo "Step 1: Compiling object file..."
cc -O2 -I/usr/include/pppd -fPIC -Wall -Wextra -std=c99 \
   $(pkg-config --cflags libcurl json-c openssl) \
   -Wno-unused-const-variable -Wno-missing-field-initializers \
   -c httpauth.c -o httpauth.o

if [ $? -eq 0 ]; then
    echo "✓ Object file compiled successfully"
else
    echo "✗ Object file compilation failed"
    exit 1
fi

echo ""
echo "Step 2: Linking plugin..."
cc -shared -o httpauth.so httpauth.o $(pkg-config --libs libcurl json-c openssl)

if [ $? -eq 0 ]; then
    echo "✓ Plugin linked successfully"
    echo "Plugin size: $(ls -lh httpauth.so | awk '{print $5}')"
else
    echo "✗ Plugin linking failed"
    exit 1
fi

echo ""
echo "Step 3: Testing plugin dependencies..."
ldd httpauth.so

echo ""
echo "Step 4: Compiling test program..."
cc -o test_auth test_auth.c $(pkg-config --cflags --libs libcurl json-c openssl)

if [ $? -eq 0 ]; then
    echo "✓ Test program compiled successfully"
else
    echo "✗ Test program compilation failed"
    exit 1
fi

echo ""
echo "=== Compilation Test Completed Successfully ==="
echo "Files created:"
echo "  httpauth.o  - Object file"
echo "  httpauth.so - Plugin library"
echo "  test_auth   - Test program"
