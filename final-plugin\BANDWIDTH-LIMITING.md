# HTTP认证插件 - 带宽限制功能详解

## 概述

HTTP认证插件支持完整的带宽限制功能，通过Linux Traffic Control (tc) 实现精确的上传/下载速度控制。

## 带宽限制机制

### 1. 基于过滤器ID的带宽控制
- 认证服务器在响应中返回 `filter_id` 参数
- 插件将 `filter_id` 设置为环境变量 `HTTP_FILTER_ID`
- ip-up脚本根据 `filter_id` 应用相应的带宽限制

### 2. 使用Linux Traffic Control (tc)
- 使用HTB (Hierarchical Token Bucket) 队列规则
- 支持上传和下载分别限速
- 支持突发流量控制

## 配置方法

### 1. 认证服务器响应格式
```json
{
    "pass": 1,
    "t": 1752454664,
    "s": "md5签名",
    "filter_id": "basic"
}
```

### 2. 预定义的带宽套餐

#### 基础套餐 (filter_id: "basic")
- **下载速度**: 2 Mbps
- **上传速度**: 512 Kbps
- **适用场景**: 基础用户、试用账户

#### 高级套餐 (filter_id: "premium")
- **下载速度**: 10 Mbps
- **上传速度**: 2 Mbps
- **适用场景**: 付费用户、企业用户

### 3. 自定义带宽套餐

编辑 `/etc/ppp/ip-up.d/http-limits` 脚本，添加新的套餐：

```bash
case "$FILTER_ID" in
    "basic")
        # 基础套餐: 2Mbps下行, 512Kbps上行
        tc qdisc add dev $INTERFACE root handle 1: htb default 10
        tc class add dev $INTERFACE parent 1: classid 1:10 htb rate 2Mbit
        tc class add dev $INTERFACE parent 1: classid 1:20 htb rate 512Kbit
        tc filter add dev $INTERFACE protocol ip parent 1:0 prio 1 u32 match ip dst $CLIENT_IP flowid 1:10
        tc filter add dev $INTERFACE protocol ip parent 1:0 prio 1 u32 match ip src $CLIENT_IP flowid 1:20
        ;;
    "premium")
        # 高级套餐: 10Mbps下行, 2Mbps上行
        tc qdisc add dev $INTERFACE root handle 1: htb default 10
        tc class add dev $INTERFACE parent 1: classid 1:10 htb rate 10Mbit
        tc class add dev $INTERFACE parent 1: classid 1:20 htb rate 2Mbit
        tc filter add dev $INTERFACE protocol ip parent 1:0 prio 1 u32 match ip dst $CLIENT_IP flowid 1:10
        tc filter add dev $INTERFACE protocol ip parent 1:0 prio 1 u32 match ip src $CLIENT_IP flowid 1:20
        ;;
    "enterprise")
        # 企业套餐: 50Mbps下行, 10Mbps上行
        tc qdisc add dev $INTERFACE root handle 1: htb default 10
        tc class add dev $INTERFACE parent 1: classid 1:10 htb rate 50Mbit
        tc class add dev $INTERFACE parent 1: classid 1:20 htb rate 10Mbit
        tc filter add dev $INTERFACE protocol ip parent 1:0 prio 1 u32 match ip dst $CLIENT_IP flowid 1:10
        tc filter add dev $INTERFACE protocol ip parent 1:0 prio 1 u32 match ip src $CLIENT_IP flowid 1:20
        ;;
    "unlimited")
        # 不限速套餐
        logger -t "HTTP-LIMITS" "用户 $CLIENT_IP 使用不限速套餐"
        ;;
esac

```

## 高级带宽控制

### 1. 带突发流量控制的配置
```bash
"advanced")
    # 高级配置: 5Mbps基础速度, 10Mbps突发速度
    tc qdisc add dev $INTERFACE root handle 1: htb default 10
    tc class add dev $INTERFACE parent 1: classid 1:10 htb rate 5Mbit ceil 10Mbit burst 15k
    tc class add dev $INTERFACE parent 1: classid 1:20 htb rate 1Mbit ceil 2Mbit burst 15k
    tc filter add dev $INTERFACE protocol ip parent 1:0 prio 1 u32 match ip dst $CLIENT_IP flowid 1:10
    tc filter add dev $INTERFACE protocol ip parent 1:0 prio 1 u32 match ip src $CLIENT_IP flowid 1:20
    ;;
```

### 2. 基于时间的动态限速
```bash
"time_based")
    # 根据时间段调整带宽
    HOUR=$(date +%H)
    if [ $HOUR -ge 9 ] && [ $HOUR -le 17 ]; then
        # 工作时间: 较低带宽
        DOWN_RATE="1Mbit"
        UP_RATE="256Kbit"
    else
        # 非工作时间: 较高带宽
        DOWN_RATE="5Mbit"
        UP_RATE="1Mbit"
    fi
    
    tc qdisc add dev $INTERFACE root handle 1: htb default 10
    tc class add dev $INTERFACE parent 1: classid 1:10 htb rate $DOWN_RATE
    tc class add dev $INTERFACE parent 1: classid 1:20 htb rate $UP_RATE
    tc filter add dev $INTERFACE protocol ip parent 1:0 prio 1 u32 match ip dst $CLIENT_IP flowid 1:10
    tc filter add dev $INTERFACE protocol ip parent 1:0 prio 1 u32 match ip src $CLIENT_IP flowid 1:20
    ;;
```

### 3. QoS优先级控制
```bash
"qos")
    # QoS配置: 优先保证重要流量
    tc qdisc add dev $INTERFACE root handle 1: htb default 30
    
    # 高优先级: VoIP, 视频会议
    tc class add dev $INTERFACE parent 1: classid 1:10 htb rate 2Mbit ceil 5Mbit prio 1
    # 中优先级: 网页浏览
    tc class add dev $INTERFACE parent 1: classid 1:20 htb rate 1Mbit ceil 3Mbit prio 2
    # 低优先级: 文件下载
    tc class add dev $INTERFACE parent 1: classid 1:30 htb rate 512Kbit ceil 1Mbit prio 3
    
    # 根据端口分类流量
    tc filter add dev $INTERFACE protocol ip parent 1:0 prio 1 u32 match ip dport 5060 0xffff flowid 1:10  # SIP
    tc filter add dev $INTERFACE protocol ip parent 1:0 prio 1 u32 match ip dport 80 0xffff flowid 1:20    # HTTP
    tc filter add dev $INTERFACE protocol ip parent 1:0 prio 3 u32 match ip dst $CLIENT_IP flowid 1:30     # 其他
    ;;
```

## 服务器端实现示例

### PHP示例
```php
<?php
// 根据用户套餐返回filter_id
function getUserBandwidthPlan($username) {
    // 从数据库查询用户套餐
    $user_plan = getUserPlan($username);
    
    switch ($user_plan) {
        case 'basic':
            return 'basic';
        case 'premium':
            return 'premium';
        case 'enterprise':
            return 'enterprise';
        default:
            return 'basic';
    }
}

// 认证响应
$response = [
    'pass' => 1,
    't' => time(),
    'filter_id' => getUserBandwidthPlan($username)
];

$response['s'] = md5($username . $password . $response['t'] . $sign_key);
echo json_encode($response);
?>
```

### Python示例
```python
def get_user_bandwidth_plan(username):
    """根据用户套餐返回filter_id"""
    user_plan = get_user_plan_from_db(username)
    
    plan_mapping = {
        'basic': 'basic',
        'premium': 'premium', 
        'enterprise': 'enterprise'
    }
    
    return plan_mapping.get(user_plan, 'basic')

# 认证响应
response = {
    'pass': 1,
    't': int(time.time()),
    'filter_id': get_user_bandwidth_plan(username)
}

response['s'] = hashlib.md5(f"{username}{password}{response['t']}{sign_key}".encode()).hexdigest()
return jsonify(response)
```

## 监控和调试

### 1. 查看当前带宽限制
```bash
# 查看队列规则
sudo tc -s qdisc show

# 查看类别配置
sudo tc -s class show dev ppp0

# 查看过滤器规则
sudo tc -s filter show dev ppp0
```

### 2. 实时监控带宽使用
```bash
# 监控接口流量
sudo iftop -i ppp0

# 查看连接状态
sudo netstat -i

# 监控tc统计
watch -n 1 'tc -s class show dev ppp0'
```

### 3. 调试脚本
```bash
# 查看脚本日志
sudo journalctl -t HTTP-LIMITS

# 手动测试脚本
sudo HTTP_FILTER_ID=basic /etc/ppp/ip-up.d/http-limits ppp0 tty 9600 *********** **********

# 清除tc规则
sudo tc qdisc del dev ppp0 root
```

## 故障排除

### 常见问题
1. **带宽限制不生效**
   - 检查tc命令是否安装: `which tc`
   - 检查脚本权限: `ls -l /etc/ppp/ip-up.d/http-limits`
   - 查看脚本日志: `journalctl -t HTTP-LIMITS`

2. **限速过于严格**
   - 调整burst参数增加突发流量
   - 使用ceil参数允许临时超速

3. **某些应用无法正常工作**
   - 添加QoS规则优先保证重要流量
   - 调整MTU设置避免包分片

### 测试方法
```bash
# 测试下载速度
wget -O /dev/null http://speedtest.example.com/100MB.bin

# 测试上传速度  
curl -T largefile.bin http://upload.example.com/

# 使用iperf测试
iperf3 -c speedtest.example.com
```

## 总结

HTTP认证插件的带宽限制功能提供了灵活而强大的流量控制能力，支持：

- ✅ **精确的上传/下载速度控制**
- ✅ **多种预定义套餐**
- ✅ **自定义带宽配置**
- ✅ **QoS优先级控制**
- ✅ **突发流量处理**
- ✅ **实时监控和调试**

通过合理配置，可以为不同用户提供差异化的网络服务质量。
