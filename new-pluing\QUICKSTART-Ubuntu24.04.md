# Quick Start Guide - HTTP Authentication Plugin for Ubuntu 24.04

This guide will help you quickly set up and use the HTTP authentication plugin on Ubuntu 24.04.

## System Requirements

- Ubuntu 24.04 LTS
- Root or sudo access
- Internet connection for downloading dependencies

## Step 1: Install Dependencies

Run the dependency installation script:

```bash
sudo ./install-deps.sh
```

This will install:
- `ppp-dev` - PPP development headers
- `libcurl4-openssl-dev` - HTTP client library
- `libjson-c-dev` - JSON parsing library
- `libssl-dev` - SSL/crypto library
- `build-essential` - Compilation tools
- `pkg-config` - Package configuration tool

## Step 2: Build the Plugin

```bash
./build.sh
```

This will:
- Check all dependencies
- Compile the plugin (`httpauth.so`)
- Build the test program (`test_auth`)

## Step 3: Install the Plugin

```bash
sudo make install
```

The plugin will be installed to:
- Plugin: `/usr/lib/pppd/[version]/httpauth.so`
- Man page: `/usr/share/man/man8/httpauth.8`

## Step 4: Test Authentication

Test the authentication API:

```bash
./test_auth testuser testpass
```

Expected output for successful authentication:
```
=== HTTP Authentication Test ===
Testing authentication for user: testuser
URL: https://testapi.softapi.cn/notify/pcm_ok
POST data: u=testuser&p=testpass
Server response: {"pass":1,"t":1752454664,"s":"673261d844d929b52a468404aac290ca"}
Pass value: 1
Timestamp: 1752454664
Signature: 673261d844d929b52a468404aac290ca
Signature input: testusertestpass1752454664hYC0ztcOKp2aZ5t0
Calculated signature: 673261d844d929b52a468404aac290ca
Received signature:   673261d844d929b52a468404aac290ca
Signature verification: PASSED
Authentication result: SUCCESS

=== Test Result ===
Authentication SUCCESSFUL
```

## Step 5: Configure PPP

### Option A: Global Configuration

Edit `/etc/ppp/options`:

```bash
sudo nano /etc/ppp/options
```

Add these lines:
```
# HTTP Authentication Plugin
plugin /usr/lib/pppd/2.4.9/httpauth.so
auth-url https://testapi.softapi.cn/notify/pcm_ok
sign-key hYC0ztcOKp2aZ5t0

# Basic PPP settings
auth
asyncmap 0
crtscts
lock
hide-password
modem
proxyarp
lcp-echo-interval 30
lcp-echo-failure 4
ms-dns 8.8.8.8
ms-dns 8.8.4.4
```

### Option B: Peer-specific Configuration

Create a peer configuration file:

```bash
sudo nano /etc/ppp/peers/httpauth-test
```

Content:
```
# Serial device
/dev/ttyUSB0

# Connection speed
115200

# HTTP Authentication
plugin /usr/lib/pppd/2.4.9/httpauth.so
auth-url https://testapi.softapi.cn/notify/pcm_ok
sign-key hYC0ztcOKp2aZ5t0

# PPP options
defaultroute
usepeerdns
persist
maxfail 0
asyncmap 0
auth
crtscts
lock
hide-password
```

## Step 6: Start PPP Connection

### Using global configuration:
```bash
sudo pppd /dev/ttyUSB0 115200
```

### Using peer configuration:
```bash
sudo pppd call httpauth-test
```

## Troubleshooting

### Check Plugin Loading

```bash
sudo pppd plugin /usr/lib/pppd/2.4.9/httpauth.so debug
```

### View Logs

```bash
sudo tail -f /var/log/syslog | grep pppd
```

### Common Issues

1. **Plugin not found**
   ```
   Plugin /usr/lib/pppd/2.4.9/httpauth.so is for pppd version 2.4.9, this is 2.4.7
   ```
   Solution: Check your pppd version and adjust the path:
   ```bash
   pppd --version
   ls /usr/lib/pppd/
   ```

2. **Authentication failed**
   ```
   HTTP Auth: Authentication failed for user 'testuser': pass=0
   ```
   Solution: Check your credentials and server response

3. **SSL/TLS errors**
   ```
   HTTP Auth: Request failed: SSL certificate problem
   ```
   Solution: For testing, the plugin disables SSL verification. For production, ensure valid certificates.

### Debug Mode

Enable detailed logging:

```bash
sudo pppd plugin /usr/lib/pppd/2.4.9/httpauth.so debug logfile /tmp/ppp.log
```

## Configuration Options

| Option | Description | Default |
|--------|-------------|---------|
| `auth-url` | Authentication server URL | `https://testapi.softapi.cn/notify/pcm_ok` |
| `sign-key` | Signature verification key | `hYC0ztcOKp2aZ5t0` |

## Server API Requirements

Your authentication server must:

1. Accept POST requests with `u` (username) and `p` (password) parameters
2. Return JSON response:
   ```json
   {
       "pass": 1,
       "t": 1752454664,
       "s": "md5_signature"
   }
   ```
3. Calculate signature as: `md5(username + password + timestamp + sign_key)`

## Security Notes

- Always use HTTPS in production
- Keep your signature key secret
- Monitor authentication logs
- Consider implementing rate limiting on your server

## Support

- Check logs: `sudo journalctl -u ppp`
- Test manually: `./test_auth username password`
- Verify plugin: `ldd /usr/lib/pppd/2.4.9/httpauth.so`

For more detailed information, see `README.md` and `man httpauth`.
