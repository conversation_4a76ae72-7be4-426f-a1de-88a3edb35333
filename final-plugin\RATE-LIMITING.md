# HTTP认证插件 - 限速功能说明

## 概述

HTTP认证插件现在集成了完整的限速功能，参考RADIUS插件的实现，提供基于时间、流量和带宽的精确限速控制。

## 限速功能特性

### 核心功能
- ✅ **会话超时** - 限制连接的最大持续时间
- ✅ **空闲超时** - 在无活动时自动断开连接
- ✅ **流量限制** - 限制用户的总流量使用
- ✅ **MTU控制** - 限制最大传输单元大小
- ✅ **过滤器支持** - 通过脚本实现高级流量控制
- ✅ **路由控制** - 自定义用户路由表

### 参考RADIUS标准
- 基于RADIUS限速属性的设计理念
- 兼容标准的限速参数
- 支持多种限速策略组合

## 配置选项

### PPP配置文件 (/etc/ppp/options.xl2tpd)
```
# 限速系统配置
default-session-timeout 3600
default-idle-timeout 600
default-max-octets 1073741824
default-max-octets-dir 0
default-framed-mtu 1400
```

### 配置参数说明
- `default-session-timeout`: 默认会话超时时间(秒)，0表示无限制
- `default-idle-timeout`: 默认空闲超时时间(秒)，0表示无限制
- `default-max-octets`: 默认流量限制(字节)，0表示无限制
- `default-max-octets-dir`: 默认流量限制方向(0=双向,1=输入,2=输出,3=最大值)
- `default-framed-mtu`: 默认MTU大小，0表示使用系统默认值

## API接口格式

### 认证响应中的限速参数
```json
{
    "pass": 1,
    "t": 1752454664,
    "s": "md5签名",
    "session_timeout": 3600,
    "idle_timeout": 600,
    "max_octets": 1073741824,
    "max_octets_dir": 0,
    "framed_mtu": 1400,
    "filter_id": "daily_limit",
    "framed_route": "************/24 ***********"
}
```

### 参数说明
- `session_timeout`: 会话超时时间(秒)
- `idle_timeout`: 空闲超时时间(秒)
- `max_octets`: 流量限制(字节)
- `max_octets_dir`: 流量限制方向(0=双向,1=输入,2=输出,3=最大值)
- `framed_mtu`: MTU大小
- `filter_id`: 过滤器ID，用于高级流量控制
- `framed_route`: 用户特定路由

## 限速机制

### 1. 会话超时
- **功能**: 限制连接的最大持续时间
- **实现**: 通过`maxconnect`变量设置
- **触发**: 达到时间限制后自动断开连接
- **示例**: `"session_timeout": 3600` 限制连接最长1小时

### 2. 空闲超时
- **功能**: 在无活动时自动断开连接
- **实现**: 通过`idle_time_limit`变量设置
- **触发**: 在指定时间内无数据传输时断开
- **示例**: `"idle_timeout": 600` 在10分钟无活动后断开

### 3. 流量限制
- **功能**: 限制用户的总流量使用
- **实现**: 通过`maxoctets`和`maxoctets_dir`变量设置
- **触发**: 达到流量限制后自动断开连接
- **示例**: `"max_octets": 1073741824` 限制总流量为1GB

### 4. MTU控制
- **功能**: 限制最大传输单元大小
- **实现**: 通过`netif_set_mtu`函数设置
- **效果**: 控制数据包大小，影响传输效率
- **示例**: `"framed_mtu": 1400` 设置MTU为1400字节

### 5. 过滤器支持
- **功能**: 通过脚本实现高级流量控制
- **实现**: 通过环境变量`HTTP_FILTER_ID`传递给脚本
- **用途**: 可实现QoS、带宽限制、访问控制等
- **示例**: `"filter_id": "daily_limit"` 应用名为"daily_limit"的过滤规则

### 6. 路由控制
- **功能**: 自定义用户路由表
- **实现**: 通过环境变量`HTTP_FRAMED_ROUTE`传递给脚本
- **用途**: 实现用户特定的路由策略
- **示例**: `"framed_route": "************/24 ***********"` 添加特定路由

## 流量限制方向说明

- **0 (双向)**: 上传和下载的总流量
- **1 (输入)**: 仅下载流量
- **2 (输出)**: 仅上传流量
- **3 (最大值)**: 上传或下载中的最大值

## 实现脚本示例

### 1. 带宽限制脚本 (tc_limit.sh)
```bash
#!/bin/bash
# 使用tc实现带宽限制
# 由HTTP_FILTER_ID触发

FILTER_ID="$HTTP_FILTER_ID"
INTERFACE="$1"
CLIENT_IP="$4"

case "$FILTER_ID" in
  "basic")
    # 基础套餐: 2Mbps下行, 512Kbps上行
    tc qdisc add dev $INTERFACE root handle 1: htb default 10
    tc class add dev $INTERFACE parent 1: classid 1:10 htb rate 2Mbit
    tc class add dev $INTERFACE parent 1: classid 1:20 htb rate 512Kbit
    tc filter add dev $INTERFACE protocol ip parent 1:0 prio 1 u32 match ip dst $CLIENT_IP flowid 1:10
    tc filter add dev $INTERFACE protocol ip parent 1:0 prio 1 u32 match ip src $CLIENT_IP flowid 1:20
    ;;
  "premium")
    # 高级套餐: 10Mbps下行, 2Mbps上行
    tc qdisc add dev $INTERFACE root handle 1: htb default 10
    tc class add dev $INTERFACE parent 1: classid 1:10 htb rate 10Mbit
    tc class add dev $INTERFACE parent 1: classid 1:20 htb rate 2Mbit
    tc filter add dev $INTERFACE protocol ip parent 1:0 prio 1 u32 match ip dst $CLIENT_IP flowid 1:10
    tc filter add dev $INTERFACE protocol ip parent 1:0 prio 1 u32 match ip src $CLIENT_IP flowid 1:20
    ;;
esac
```

### 2. 自定义路由脚本 (add_routes.sh)
```bash
#!/bin/bash
# 添加用户特定路由
# 由HTTP_FRAMED_ROUTE触发

if [ -n "$HTTP_FRAMED_ROUTE" ]; then
  # 格式: "网络/掩码 网关"
  read -r NETWORK GATEWAY <<< "$HTTP_FRAMED_ROUTE"
  ip route add $NETWORK via $GATEWAY
  echo "Added route: $NETWORK via $GATEWAY" >> /var/log/ppp-routes.log
fi
```

## 使用方法

### 1. 配置默认限速参数
编辑 `/etc/ppp/options.xl2tpd` 添加:
```
default-session-timeout 3600
default-idle-timeout 600
default-max-octets 1073741824
default-max-octets-dir 0
```

### 2. 修改认证服务器响应
在认证服务器返回的JSON中添加限速参数:
```json
{
    "pass": 1,
    "t": 1752454664,
    "s": "md5签名",
    "session_timeout": 7200,
    "idle_timeout": 1800,
    "max_octets": 5368709120,
    "filter_id": "premium"
}
```

### 3. 创建ip-up脚本
创建 `/etc/ppp/ip-up.d/http-limits` 脚本:
```bash
#!/bin/bash
# 应用HTTP认证插件的限速设置

if [ -n "$HTTP_FILTER_ID" ]; then
  /etc/ppp/tc_limit.sh "$1" "$2" "$3" "$4" "$5" "$6"
fi

if [ -n "$HTTP_FRAMED_ROUTE" ]; then
  /etc/ppp/add_routes.sh
fi
```

## 故障排除

### 常见问题
1. **限速不生效** - 检查pppd是否使用了正确的配置文件
2. **流量限制不工作** - 确认pppd编译时启用了MAXOCTETS选项
3. **脚本未执行** - 检查ip-up脚本权限和路径

### 调试方法
1. 启用debug模式查看详细日志
2. 检查环境变量是否正确传递给脚本
3. 使用tc命令验证QoS规则是否生效

## 总结

集成的限速系统提供了完整的用户访问控制功能，与RADIUS标准兼容，为VPN服务提供了精确的限速能力。通过HTTP API的方式，可以轻松集成到现有的计费和控制系统中。
