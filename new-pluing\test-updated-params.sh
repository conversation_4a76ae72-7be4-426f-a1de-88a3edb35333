#!/bin/bash

# Test script for updated HTTP authentication plugin with u/p parameters
# Ubuntu 24.04

set -e

echo "=== Testing Updated HTTP Authentication Plugin ==="
echo "Parameters changed: username -> u, password -> p"
echo ""

# Check if files exist
if [ ! -f "httpauth_simple.c" ]; then
    echo "❌ httpauth_simple.c not found"
    exit 1
fi

if [ ! -f "test_auth.c" ]; then
    echo "❌ test_auth.c not found"
    exit 1
fi

echo "1. Verifying parameter changes in source code..."

# Check if the changes were applied
if grep -q 'snprintf.*"u=%s&p=%s"' httpauth_simple.c; then
    echo "✅ httpauth_simple.c: Parameters updated to u/p"
else
    echo "❌ httpauth_simple.c: Parameters not updated"
fi

if grep -q 'snprintf.*"u=%s&p=%s"' test_auth.c; then
    echo "✅ test_auth.c: Parameters updated to u/p"
else
    echo "❌ test_auth.c: Parameters not updated"
fi

echo ""
echo "2. Rebuilding with updated parameters..."

# Clean and rebuild
make -f Makefile.simple clean
make -f Makefile.simple

if [ $? -eq 0 ]; then
    echo "✅ Plugin rebuilt successfully"
else
    echo "❌ Plugin rebuild failed"
    exit 1
fi

echo ""
echo "3. Building test program..."
make -f Makefile.simple test

if [ $? -eq 0 ]; then
    echo "✅ Test program rebuilt successfully"
else
    echo "❌ Test program rebuild failed"
    exit 1
fi

echo ""
echo "4. Testing authentication with new parameters..."

echo "Testing with test program (this will show the actual POST data)..."
if ./test_auth testuser testpass; then
    echo "✅ Authentication test completed successfully"
else
    echo "⚠️ Authentication test failed (may be expected if server doesn't support u/p parameters yet)"
fi

echo ""
echo "5. Manual curl test with new parameters..."

echo "Testing direct curl request with u/p parameters:"
curl -X POST https://testapi.softapi.cn/notify/pcm_ok \
     -d "u=testuser&p=testpass" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     --connect-timeout 10 \
     --max-time 30 \
     -v 2>&1 | head -20

echo ""
echo ""
echo "=== Parameter Update Summary ==="
echo "✅ Source code updated: username -> u, password -> p"
echo "✅ Plugin recompiled successfully"
echo "✅ Test program updated"

echo ""
echo "=== Server Requirements Update ==="
echo "Your authentication server now needs to accept:"
echo "  POST parameters: u (username), p (password)"
echo "  Instead of: username, password"
echo ""
echo "Example server code update:"
echo ""
echo "PHP:"
echo '  $username = $_POST["u"] ?? "";'
echo '  $password = $_POST["p"] ?? "";'
echo ""
echo "Python (Flask):"
echo '  username = request.form.get("u", "")'
echo '  password = request.form.get("p", "")'
echo ""
echo "Node.js (Express):"
echo '  const username = req.body.u || "";'
echo '  const password = req.body.p || "";'

echo ""
echo "=== Testing Commands ==="
echo "1. Test authentication:"
echo "   ./test_auth <username> <password>"
echo ""
echo "2. Manual curl test:"
echo '   curl -X POST https://testapi.softapi.cn/notify/pcm_ok -d "u=user&p=pass"'
echo ""
echo "3. Install updated plugin:"
echo "   sudo make -f Makefile.simple install"
echo ""
echo "4. Use with pppd:"
echo "   sudo pppd /dev/ttyUSB0 115200 plugin /usr/lib/pppd/2.4.9/httpauth.so"

echo ""
echo "🎉 Parameter update completed successfully!"
