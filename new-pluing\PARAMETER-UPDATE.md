# 参数更新说明

## 更新内容

HTTP认证插件的POST参数已更新：

### 旧参数格式
```
username=用户名&password=密码
```

### 新参数格式
```
u=用户名&p=密码
```

## 影响的文件

以下文件已更新以支持新的参数格式：

1. **httpauth_simple.c** - 主插件源码
2. **httpauth.c** - 原版插件源码
3. **test_auth.c** - 测试程序
4. **README.md** - 文档更新
5. **QUICKSTART-Ubuntu24.04.md** - 快速开始指南
6. **example-config.txt** - 配置示例

## 服务器端更新

### PHP示例
```php
<?php
// 旧版本
$username = $_POST['username'] ?? '';
$password = $_POST['password'] ?? '';

// 新版本
$username = $_POST['u'] ?? '';
$password = $_POST['p'] ?? '';

// 签名计算保持不变
$signature = md5($username . $password . $timestamp . $sign_key);
?>
```

### Python Flask示例
```python
# 旧版本
username = request.form.get('username', '')
password = request.form.get('password', '')

# 新版本
username = request.form.get('u', '')
password = request.form.get('p', '')

# 签名计算保持不变
signature = hashlib.md5(f"{username}{password}{timestamp}{sign_key}".encode()).hexdigest()
```

### Node.js Express示例
```javascript
// 旧版本
const username = req.body.username || '';
const password = req.body.password || '';

// 新版本
const username = req.body.u || '';
const password = req.body.p || '';

// 签名计算保持不变
const signature = crypto.createHash('md5').update(username + password + timestamp + signKey).digest('hex');
```

## 测试更新

### 1. 重新编译插件
```bash
cd new-pluing
make -f Makefile.simple clean
make -f Makefile.simple
```

### 2. 测试新参数
```bash
./test_auth testuser testpass
```

输出应显示：
```
POST data: u=testuser&p=testpass
```

### 3. 手动curl测试
```bash
curl -X POST https://testapi.softapi.cn/notify/pcm_ok \
     -d "u=testuser&p=testpass" \
     -H "Content-Type: application/x-www-form-urlencoded"
```

### 4. 运行更新测试脚本
```bash
./test-updated-params.sh
```

## API接口更新

### 请求格式
```
POST https://testapi.softapi.cn/notify/pcm_ok
Content-Type: application/x-www-form-urlencoded

u=用户名&p=密码
```

### 响应格式（保持不变）
```json
{
    "pass": 1,
    "t": 1752454664,
    "s": "673261d844d929b52a468404aac290ca"
}
```

### 签名计算（保持不变）
```
md5(username + password + timestamp + "hYC0ztcOKp2aZ5t0")
```

## 兼容性说明

### 向后兼容性
- 新版本插件**不兼容**旧的服务器端代码
- 服务器端必须更新以接受`u`和`p`参数
- 签名计算逻辑保持不变

### 升级步骤
1. 更新插件代码并重新编译
2. 更新服务器端代码以接受新参数
3. 测试认证功能
4. 部署更新

## 服务器端示例文件

提供了完整的服务器端示例：

1. **server-example-updated.php** - PHP示例服务器
2. **server-example-updated.py** - Python Flask示例服务器

### 运行PHP示例
```bash
# 将server-example-updated.php放到Web服务器目录
# 访问: http://your-server/server-example-updated.php
```

### 运行Python示例
```bash
pip install flask
python server-example-updated.py
# 访问: http://localhost:5000/notify/pcm_ok
```

## 测试服务器

Python示例服务器包含测试用户：
- `testuser` / `testpass`
- `admin` / `admin123`
- `user1` / `password1`
- `demo` / `demo`

测试命令：
```bash
curl -X POST http://localhost:5000/notify/pcm_ok -d "u=testuser&p=testpass"
```

## 故障排除

### 1. 参数未更新
检查源码中是否包含新的参数格式：
```bash
grep -n "u=%s&p=%s" httpauth_simple.c
```

### 2. 服务器返回错误
确保服务器端代码已更新以接受`u`和`p`参数

### 3. 签名验证失败
签名计算逻辑保持不变，检查：
- 用户名和密码是否正确
- 时间戳是否正确
- sign_key是否匹配

### 4. 测试失败
运行详细测试：
```bash
./test-updated-params.sh
```

## 更新日志

- **2024-01-XX**: 参数格式从`username/password`更新为`u/p`
- 更新了所有相关文档和示例代码
- 提供了服务器端更新示例
- 添加了兼容性测试脚本

## 注意事项

1. **必须同时更新客户端和服务器端**
2. **签名计算逻辑保持不变**
3. **测试所有功能确保正常工作**
4. **更新部署文档和运维手册**

这个更新提高了参数的简洁性，减少了网络传输的数据量，但需要确保服务器端同步更新。
