#!/bin/bash

# Create correct peer configuration for Ubuntu 24.04
# This script creates a working pppd peer configuration

set -e

if [ "$EUID" -ne 0 ]; then
    echo "This script needs to be run with sudo"
    echo "Usage: sudo ./create-peer-config.sh"
    exit 1
fi

echo "=== Creating PPP Peer Configuration for Ubuntu 24.04 ==="

# Get pppd version and plugin path
PPPD_VERSION=$(pppd --version 2>&1 | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+' | head -1 || echo "2.4.9")
PLUGIN_PATH="/usr/lib/pppd/$PPPD_VERSION/httpauth.so"

echo "PPP version: $PPPD_VERSION"
echo "Plugin path: $PLUGIN_PATH"

# Check if plugin exists
if [ ! -f "$PLUGIN_PATH" ]; then
    echo "Error: Plugin not found at $PLUGIN_PATH"
    echo "Please install the plugin first with: sudo make -f Makefile.simple install"
    exit 1
fi

# Create the peer configuration
PEER_FILE="/etc/ppp/peers/httpauth"

echo "Creating peer configuration: $PEER_FILE"

cat > "$PEER_FILE" << 'EOF'
# HTTP Authentication Peer Configuration for Ubuntu 24.04
# Usage: sudo pppd call httpauth

# Connection settings
115200
defaultroute
usepeerdns
persist
maxfail 0

# PPP protocol options
asyncmap 0
auth
crtscts
lock
hide-password
noipdefault

# HTTP Authentication Plugin
plugin /usr/lib/pppd/2.4.9/httpauth.so
auth-url https://testapi.softapi.cn/notify/pcm_ok
sign-key hYC0ztcOKp2aZ5t0

# Uncomment and adjust the device as needed
# /dev/ttyUSB0
# /dev/ttyS0
# /dev/ttyACM0

# Uncomment for debugging
# debug
# logfile /tmp/ppp-httpauth.log
EOF

# Update plugin path in the file
sed -i "s|/usr/lib/pppd/2.4.9/httpauth.so|$PLUGIN_PATH|g" "$PEER_FILE"

echo "✓ Peer configuration created: $PEER_FILE"

# Create a version with device specified
PEER_FILE_USB="/etc/ppp/peers/httpauth-usb"

cat > "$PEER_FILE_USB" << EOF
# HTTP Authentication with USB modem
# Usage: sudo pppd call httpauth-usb

# Device and speed
/dev/ttyUSB0
115200

# Connection settings
defaultroute
usepeerdns
persist
maxfail 0

# PPP protocol options
asyncmap 0
auth
crtscts
lock
hide-password
noipdefault

# HTTP Authentication Plugin
plugin $PLUGIN_PATH
auth-url https://testapi.softapi.cn/notify/pcm_ok
sign-key hYC0ztcOKp2aZ5t0

# Uncomment for debugging
# debug
# logfile /tmp/ppp-httpauth.log
EOF

echo "✓ USB modem configuration created: $PEER_FILE_USB"

# Create a debug version
PEER_FILE_DEBUG="/etc/ppp/peers/httpauth-debug"

cat > "$PEER_FILE_DEBUG" << EOF
# HTTP Authentication with debug enabled
# Usage: sudo pppd call httpauth-debug

# Device and speed
/dev/ttyUSB0
115200

# Connection settings
defaultroute
usepeerdns
persist
maxfail 0

# PPP protocol options
asyncmap 0
auth
crtscts
lock
hide-password
noipdefault

# HTTP Authentication Plugin
plugin $PLUGIN_PATH
auth-url https://testapi.softapi.cn/notify/pcm_ok
sign-key hYC0ztcOKp2aZ5t0

# Debug options
debug
logfile /tmp/ppp-httpauth.log
EOF

echo "✓ Debug configuration created: $PEER_FILE_DEBUG"

echo ""
echo "=== Configuration Files Created ==="
echo "1. $PEER_FILE"
echo "   - Basic configuration without device specified"
echo "   - Usage: sudo pppd call httpauth /dev/ttyUSB0"
echo ""
echo "2. $PEER_FILE_USB"
echo "   - Configuration with USB modem (/dev/ttyUSB0)"
echo "   - Usage: sudo pppd call httpauth-usb"
echo ""
echo "3. $PEER_FILE_DEBUG"
echo "   - Debug configuration with logging"
echo "   - Usage: sudo pppd call httpauth-debug"
echo ""
echo "=== Usage Examples ==="
echo ""
echo "Method 1 - Specify device on command line:"
echo "  sudo pppd call httpauth /dev/ttyUSB0"
echo ""
echo "Method 2 - Use preconfigured device:"
echo "  sudo pppd call httpauth-usb"
echo ""
echo "Method 3 - Debug mode:"
echo "  sudo pppd call httpauth-debug"
echo "  tail -f /tmp/ppp-httpauth.log"
echo ""
echo "Method 4 - Direct command (no peer file):"
echo "  sudo pppd /dev/ttyUSB0 115200 \\"
echo "    plugin $PLUGIN_PATH \\"
echo "    auth-url https://testapi.softapi.cn/notify/pcm_ok \\"
echo "    sign-key hYC0ztcOKp2aZ5t0 \\"
echo "    defaultroute usepeerdns"
echo ""
echo "=== Troubleshooting ==="
echo "- Check available devices: ls -la /dev/tty*"
echo "- Test plugin loading: sudo pppd plugin $PLUGIN_PATH nodetach"
echo "- View system logs: sudo journalctl -f | grep pppd"
echo "- Test authentication: test_auth username password"

echo ""
echo "Configuration complete!"
