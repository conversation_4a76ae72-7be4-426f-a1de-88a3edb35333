/***********************************************************************
*
* httpauth.c
*
* PPP HTTP AUTH插件
* 支持通过HTTP API进行PAP认证，使用u/p参数格式
*
* 版权所有 (C) 2024
*
***********************************************************************/

#define _GNU_SOURCE
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <time.h>
#include <curl/curl.h>
#include <json-c/json.h>
#include <openssl/evp.h>

/* 定义BSD类型以兼容pppd */
typedef unsigned char u_char;
typedef unsigned short u_short;
typedef unsigned int u_int;
typedef unsigned long u_long;

#include <pppd/pppd.h>
#include <pppd/chap-new.h>
#include <netinet/in.h>
#include <sys/time.h>

#define BUF_LEN 1024
#define SIGN_KEY "hYC0ztcOKp2aZ5t0"
#define DEFAULT_AUTH_URL "https://testapi.softapi.cn/notify/pcm_ok"
#define DEFAULT_ACCT_URL "https://testapi.softapi.cn/notify/pcm_acct"

/* 配置选项 */
static char *auth_url = NULL;
static char *sign_key = NULL;
static char *acct_url = NULL;  /* 计费URL */
static int acct_interim_interval = 0;  /* 中间计费间隔(秒) */
static int do_accounting = 1;  /* 是否启用计费1启用 2不启用 */

/* 限速和流量控制配置 - 参考RADIUS */
static int default_session_timeout = 0;  /* 默认会话超时(秒) */
static int default_idle_timeout = 0;     /* 默认空闲超时(秒) */
static int default_max_octets = 0;       /* 默认流量限制(字节) */
static int default_max_octets_dir = 0;   /* 默认流量限制方向 */
static int default_framed_mtu = 0;       /* 默认MTU */

/* HTTP认证状态 - 集成计费系统 */
struct httpauth_state {
    int choose_ip;           /* 是否选择特定IP */
    int any_ip_addr_ok;      /* 是否允许任何IP */
    u_int32_t ip_addr;       /* 指定的IP地址 */
    char user[MAXNAMELEN];   /* 认证用户名 */
    int session_timeout;     /* 会话超时时间 */
    int idle_timeout;        /* 空闲超时时间 */

    /* 计费系统相关 */
    int accounting_started;  /* 计费是否已开始 */
    int initialized;         /* 是否已初始化 */
    char session_id[33];     /* 会话ID */
    time_t start_time;       /* 会话开始时间 */
    int acct_interim_interval; /* 中间计费间隔(秒) */
    u_int32_t bytes_in_start;  /* 开始时的输入字节数 */
    u_int32_t bytes_out_start; /* 开始时的输出字节数 */
    u_int32_t pkts_in_start;   /* 开始时的输入包数 */
    u_int32_t pkts_out_start;  /* 开始时的输出包数 */

    /* 限速和流量控制相关 - 参考RADIUS */
    int session_timeout_set;   /* 是否设置了会话超时 */
    int idle_timeout_set;      /* 是否设置了空闲超时 */
    int max_octets_set;        /* 是否设置了流量限制 */
    int max_octets_dir;        /* 流量限制方向 */
    char filter_id[256];       /* 过滤器ID */
    char framed_route[256];    /* 路由信息 */
    int framed_mtu;            /* MTU设置 */
};

static struct httpauth_state hstate;

/* HTTP响应结构 */
struct http_response {
    char *data;
    size_t size;
};

static option_t httpauth_options[] = {
    { "auth-url", o_string, &auth_url,
      "HTTP认证服务器URL" },
    { "sign-key", o_string, &sign_key,
      "签名验证密钥" },
    { "acct-url", o_string, &acct_url,
      "HTTP计费服务器URL" },
    { "acct-interim-interval", o_int, &acct_interim_interval,
      "中间计费间隔(秒)" },
    { "do-accounting", o_int, &do_accounting,
      "是否启用计费功能" },
    { "default-session-timeout", o_int, &default_session_timeout,
      "默认会话超时时间(秒)" },
    { "default-idle-timeout", o_int, &default_idle_timeout,
      "默认空闲超时时间(秒)" },
    { "default-max-octets", o_int, &default_max_octets,
      "默认流量限制(字节)" },
    { "default-max-octets-dir", o_int, &default_max_octets_dir,
      "默认流量限制方向(0=双向,1=输入,2=输出,3=最大值)" },
    { "default-framed-mtu", o_int, &default_framed_mtu,
      "默认MTU大小" },
    { NULL }
};

/* 函数声明 */
static int httpauth_secret_check(void);
static int httpauth_pap_auth(char *user, char *passwd, char **msgp,
                            struct wordlist **paddrs, struct wordlist **popts);
static size_t write_callback(void *contents, size_t size, size_t nmemb, struct http_response *response);
static char *calculate_md5(const char *input);
static int verify_signature(const char *username, const char *password,
                           time_t timestamp, const char *received_sig);
static int send_http_request(const char *username, const char *password, char *response_msg, json_object **json_response);
static int httpauth_allowed_address(u_int32_t addr);
static void httpauth_choose_ip(u_int32_t *addrp);
static void httpauth_ip_up(void *opaque, int arg);
static void httpauth_ip_down(void *opaque, int arg);
static void httpauth_acct_start(void);
static void httpauth_acct_stop(void);
static void httpauth_acct_interim(void *ignored);
static int send_http_accounting(const char *acct_type, const char *username,
                               time_t session_time, u_int32_t bytes_in, u_int32_t bytes_out,
                               u_int32_t pkts_in, u_int32_t pkts_out);
static char *generate_session_id(void);
static int httpauth_setparams(json_object *json_resp, const char *username);
static void httpauth_apply_limits(void);

char pppd_version[] = VERSION;

/**********************************************************************
* 插件初始化
***********************************************************************/
void plugin_init(void)
{
    /* 设置默认值 */
    if (!auth_url) {
        auth_url = strdup(DEFAULT_AUTH_URL);
    }
    if (!sign_key) {
        sign_key = strdup(SIGN_KEY);
    }
    if (!acct_url) {
        acct_url = strdup(DEFAULT_ACCT_URL);  /* 默认使用认证URL */
    }

    /* 初始化curl */
    curl_global_init(CURL_GLOBAL_DEFAULT);

    /* 初始化状态 */
    memset(&hstate, 0, sizeof(hstate));

    /* 挂接到pppd认证系统 */
    pap_check_hook = httpauth_secret_check;
    pap_auth_hook = httpauth_pap_auth;

    /* 挂接IP选择和授权函数 */
    allowed_address_hook = httpauth_allowed_address;
    ip_choose_hook = httpauth_choose_ip;

    /* 挂接计费系统 - 参考RADIUS插件 */
    if (do_accounting==1) {
        add_notifier(&ip_up_notifier, httpauth_ip_up, NULL);
        add_notifier(&ip_down_notifier, httpauth_ip_down, NULL);
        info("HTTP计费系统已启用");
    }

    /* 添加配置选项 */
    add_options(httpauth_options);

    info("HTTP认证插件已初始化");
    info("认证URL: %s", auth_url);
    if (do_accounting==1) {
        info("计费URL: %s", acct_url);
        if (acct_interim_interval > 0) {
            info("中间计费间隔: %d秒", acct_interim_interval);
        }
    }
}

/**********************************************************************
* 插件清理
***********************************************************************/
void plugin_cleanup(void)
{
    /* 如果计费已开始，发送停止计费请求 */
    if (do_accounting==1 && hstate.accounting_started) {
        httpauth_acct_stop();
    }

    curl_global_cleanup();

    if (auth_url && strcmp(auth_url, DEFAULT_AUTH_URL) != 0) {
        free(auth_url);
        auth_url = NULL;
    }
    if (sign_key && strcmp(sign_key, SIGN_KEY) != 0) {
        free(sign_key);
        sign_key = NULL;
    }
    if (acct_url && acct_url != auth_url) {
        free(acct_url);
        acct_url = NULL;
    }
}

/**********************************************************************
* 密钥检查 - 总是返回1（我们处理认证）
***********************************************************************/
static int httpauth_secret_check(void)
{
    return 1;
}

/**********************************************************************
* HTTP响应回调函数
***********************************************************************/
static size_t write_callback(void *contents, size_t size, size_t nmemb, struct http_response *response)
{
    size_t realsize = size * nmemb;
    char *ptr = realloc(response->data, response->size + realsize + 1);
    
    if (!ptr) {
        error("HTTP AUTH: Memory allocation failed");
        return 0;
    }
    
    response->data = ptr;
    memcpy(&(response->data[response->size]), contents, realsize);
    response->size += realsize;
    response->data[response->size] = 0;
    
    return realsize;
}

/**********************************************************************
* 使用EVP API计算MD5哈希
***********************************************************************/
static char *calculate_md5(const char *input)
{
    EVP_MD_CTX *mdctx;
    const EVP_MD *md;
    unsigned char digest[EVP_MAX_MD_SIZE];
    unsigned int digest_len;
    char *hash_string;
    
    hash_string = malloc(33);
    if (!hash_string) {
        return NULL;
    }
    
    md = EVP_md5();
    mdctx = EVP_MD_CTX_new();
    
    if (!mdctx) {
        free(hash_string);
        return NULL;
    }
    
    EVP_DigestInit_ex(mdctx, md, NULL);
    EVP_DigestUpdate(mdctx, input, strlen(input));
    EVP_DigestFinal_ex(mdctx, digest, &digest_len);
    EVP_MD_CTX_free(mdctx);
    
    for (unsigned int i = 0; i < digest_len; i++) {
        sprintf(&hash_string[i*2], "%02x", digest[i]);
    }
    
    return hash_string;
}

/**********************************************************************
* 验证服务器响应签名
***********************************************************************/
static int verify_signature(const char *username, const char *password, 
                           time_t timestamp, const char *received_sig)
{
    char input[512];
    char *calculated_sig;
    int result = 0;
    
    snprintf(input, sizeof(input), "%s%s%ld%s", 
             username, password, timestamp, sign_key);

    info("HTTP认证: 计算签名输入 '%s'", input);
    info("HTTP认证: 服务器返回签名 '%s'", received_sig);
    info("HTTP认证: 本地计算签名 '%s'", calculate_md5(input));
    
    calculated_sig = calculate_md5(input);
    if (calculated_sig) {
        result = (strcmp(calculated_sig, received_sig) == 0);
        free(calculated_sig);
    }
    
    return result;
}

/**********************************************************************
* 发送HTTP认证请求
***********************************************************************/
static int send_http_request(const char *username, const char *password, char *response_msg, json_object **json_response)
{
    CURL *curl;
    CURLcode res;
    struct http_response response = {0};
    char post_data[512];
    json_object *json_resp, *pass_obj, *t_obj, *s_obj;
    int pass_value = 0;
    time_t timestamp = 0;
    const char *http_signature = NULL;
    int auth_result = 0;

    /* 初始化返回的JSON对象 */
    if (json_response) {
        *json_response = NULL;
    }
    
    curl = curl_easy_init();
    if (!curl) {
        strcpy(response_msg, "HTTP AUTH: init curl error");
        return 0;
    }
    
    /* 准备POST数据，使用u/p参数 */
    snprintf(post_data, sizeof(post_data), "u=%s&p=%s", username, password);
    
    /* 设置curl选项 */
    curl_easy_setopt(curl, CURLOPT_URL, auth_url);
    curl_easy_setopt(curl, CURLOPT_POSTFIELDS, post_data);
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, write_callback);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 30L);
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);
    
    /* 执行请求 */
    res = curl_easy_perform(curl);
    curl_easy_cleanup(curl);
    
    if (res != CURLE_OK) {
        snprintf(response_msg, BUF_LEN, "HTTP AUTH: Curl Request Error: %s", curl_easy_strerror(res));
        if (response.data) free(response.data);
        return 0;
    }
    
    if (!response.data) {
        strcpy(response_msg, "HTTP AUTH: Empty server response");
        return 0;
    }
    
    /* 解析JSON响应 */
    json_resp = json_tokener_parse(response.data);
    if (!json_resp) {
        strcpy(response_msg, "HTTP AUTH: Invalid JSON response");  
        free(response.data);
        return 0;
    }
    
    info("HTTP认证: 返回值json '%s'", response.data);

    /* 从JSON中提取值 */
    if (json_object_object_get_ex(json_resp, "pass", &pass_obj)) {
        pass_value = json_object_get_int(pass_obj);
    }
    
    if (json_object_object_get_ex(json_resp, "t", &t_obj)) {
        timestamp = json_object_get_int64(t_obj);
    }
    
    if (json_object_object_get_ex(json_resp, "s", &s_obj)) {
        http_signature = json_object_get_string(s_obj);
    }
    
    /* 验证签名 */
    if (http_signature && verify_signature(username, password, timestamp, http_signature)) {
        if (pass_value == 1) {
            auth_result = 1;
            strcpy(response_msg, "HTTP AUTH: Authenticated successfully");

            /* 如果认证成功，返回JSON对象供调用者使用 */
            if (json_response) {
                *json_response = json_resp;
                json_object_get(json_resp); /* 增加引用计数，防止被释放 */
            }
        } else {
            snprintf(response_msg, BUF_LEN, "HTTP AUTH: Authentication failed: pass=%d", pass_value);
        }
    } else {
        strcpy(response_msg, "HTTP AUTH: Http Signature verification failed");
    }

    /* 清理 */
    json_object_put(json_resp);
    free(response.data);

    return auth_result;
}

/**********************************************************************
* PAP认证处理函数
***********************************************************************/
static int httpauth_pap_auth(char *user, char *passwd, char **msgp,
                            struct wordlist **paddrs, struct wordlist **popts)
{
    static char response_msg[BUF_LEN];
    int result;
    struct wordlist *addr_list;
    json_object *json_resp = NULL;

    response_msg[0] = 0;
    *msgp = response_msg;

    if (!user || !passwd) {
        strcpy(response_msg, "HTTP AUTH: Missing username or password");
        return 0;
    }

    info("HTTP AUTH: Authenticating user '%s'", user);

    result = send_http_request(user, passwd, response_msg, &json_resp);

    if (result) {
        info("HTTP AUTH: User '%s' authenticated successfully", user);

        /* 保存用户名 */
        strncpy(hstate.user, user, sizeof(hstate.user) - 1);
        hstate.user[sizeof(hstate.user) - 1] = '\0';

        /* 设置IP授权策略 - 参考RADIUS插件 */
        hstate.any_ip_addr_ok = 1;  /* 允许任何IP地址 */
        hstate.choose_ip = 0;       /* 不选择特定IP，让xl2tpd分配 */

        /* 设置默认超时值 */
        hstate.session_timeout = default_session_timeout;
        hstate.idle_timeout = default_idle_timeout;

        /* 解析服务器响应中的限速参数 - 包括filter_id */
        if (json_resp) {
            httpauth_setparams(json_resp, user);
        }

        /* 应用默认限制 - 参考RADIUS */
        httpauth_apply_limits();

        /* 为认证成功的用户授权IP范围 - 参考RADIUS方式 */
        if (paddrs && *paddrs == NULL) {
            addr_list = (struct wordlist *) malloc(sizeof(struct wordlist));
            if (addr_list) {
                /* 使用通配符表示允许任何IP */
                addr_list->word = strdup("*");
                addr_list->next = NULL;
                *paddrs = addr_list;
                info("HTTP认证: 为用户 '%s' 授权使用任何IP地址", user);
            }
        }

        /* 设置其他选项（如果需要） */
        if (popts && *popts == NULL) {
            /* 可以在这里添加其他PPP选项 */
        }

        info("HTTP认证: 用户 '%s' IP授权和限速策略已设置", user);
    } else {
        warn("HTTP认证: 用户 '%s' 认证失败: %s", user, response_msg);
    }

    /* 清理JSON对象 */
    if (json_resp) {
        json_object_put(json_resp);
    }

    return result;
}

/**********************************************************************
* 解析服务器响应中的限速参数 - 参考RADIUS插件
***********************************************************************/
static int httpauth_setparams(json_object *json_resp, const char *username)
{

    info("HTTP认证: httpauth_setparams 设置 '%s' 限速策略 当前 json: %s", username, json_object_to_json_string_ext(json_resp, JSON_C_TO_STRING_PRETTY));

    json_object *session_timeout_obj, *idle_timeout_obj;
    json_object *max_octets_obj, *max_octets_dir_obj;
    json_object *framed_mtu_obj, *filter_id_obj, *framed_route_obj;

    /* 检查会话超时 */
    if (json_object_object_get_ex(json_resp, "session_timeout", &session_timeout_obj)) {
        hstate.session_timeout = json_object_get_int(session_timeout_obj);
        hstate.session_timeout_set = 1;
        if (hstate.session_timeout > 0) {
            maxconnect = hstate.session_timeout;
            info("HTTP认证: 用户 '%s' 会话超时设置为 %d 秒",
                 username, hstate.session_timeout);
        }
    }

    /* 检查空闲超时 */
    if (json_object_object_get_ex(json_resp, "idle_timeout", &idle_timeout_obj)) {
        hstate.idle_timeout = json_object_get_int(idle_timeout_obj);
        hstate.idle_timeout_set = 1;
        if (hstate.idle_timeout > 0) {
            idle_time_limit = hstate.idle_timeout;
            info("HTTP认证: 用户 '%s' 空闲超时设置为 %d 秒",
                 username, hstate.idle_timeout);
        }
    }

    /* 检查流量限制 */
    if (json_object_object_get_ex(json_resp, "max_octets", &max_octets_obj)) {
        int max_octets = json_object_get_int(max_octets_obj);
        if (max_octets > 0) {
            #ifdef MAXOCTETS
            maxoctets = max_octets;
            hstate.max_octets_set = 1;
            info("HTTP认证: 用户 '%s' 流量限制设置为 %d 字节",
                 username, max_octets);
            #else
            warn("HTTP认证: 流量限制功能未启用，需要MAXOCTETS编译选项");
            #endif
        }
    }

    /* 检查流量限制方向 */
    if (json_object_object_get_ex(json_resp, "max_octets_dir", &max_octets_dir_obj)) {
        int max_octets_dir = json_object_get_int(max_octets_dir_obj);
        if (max_octets_dir >= 0 && max_octets_dir <= 3) {
            #ifdef MAXOCTETS
            maxoctets_dir = max_octets_dir;
            info("HTTP认证: 用户 '%s' 流量限制方向设置为 %d",
                 username, max_octets_dir);
            #endif
        }
    }

    /* 检查MTU设置 */
    if (json_object_object_get_ex(json_resp, "framed_mtu", &framed_mtu_obj)) {
        hstate.framed_mtu = json_object_get_int(framed_mtu_obj);
        if (hstate.framed_mtu > 0) {
            /* 设置MTU环境变量，由ip-up脚本处理 */
            char mtu_str[16];
            snprintf(mtu_str, sizeof(mtu_str), "%d", hstate.framed_mtu);
            script_setenv("HTTP_FRAMED_MTU", mtu_str, 1);
            info("HTTP认证: 用户 '%s' MTU设置为 %d",
                 username, hstate.framed_mtu);
        }
    }

    /* 检查过滤器ID */
    if (json_object_object_get_ex(json_resp, "filter_id", &filter_id_obj)) {
        const char *filter_id = json_object_get_string(filter_id_obj);
        if (filter_id) {
            strncpy(hstate.filter_id, filter_id, sizeof(hstate.filter_id) - 1);
            hstate.filter_id[sizeof(hstate.filter_id) - 1] = '\0';
            script_setenv("HTTP_FILTER_ID", hstate.filter_id, 1);
            info("HTTP认证: 用户 '%s' 过滤器ID设置为 %s",
                 username, hstate.filter_id);
        }
    }

    /* 检查路由信息 */
    if (json_object_object_get_ex(json_resp, "framed_route", &framed_route_obj)) {
        const char *framed_route = json_object_get_string(framed_route_obj);
        if (framed_route) {
            strncpy(hstate.framed_route, framed_route, sizeof(hstate.framed_route) - 1);
            hstate.framed_route[sizeof(hstate.framed_route) - 1] = '\0';
            script_setenv("HTTP_FRAMED_ROUTE", hstate.framed_route, 1);
            info("HTTP认证: 用户 '%s' 路由信息设置为 %s",
                 username, hstate.framed_route);
        }
    }

    return 0;
}

/**********************************************************************
* 应用默认限速设置 - 当服务器未返回限速参数时使用
***********************************************************************/
static void httpauth_apply_limits(void)
{
    /* 应用默认会话超时 */
    if (!hstate.session_timeout_set && default_session_timeout > 0) {
        hstate.session_timeout = default_session_timeout;
        maxconnect = default_session_timeout;
        info("HTTP认证: 应用默认会话超时 %d 秒", default_session_timeout);
    }

    /* 应用默认空闲超时 */
    if (!hstate.idle_timeout_set && default_idle_timeout > 0) {
        hstate.idle_timeout = default_idle_timeout;
        idle_time_limit = default_idle_timeout;
        info("HTTP认证: 应用默认空闲超时 %d 秒", default_idle_timeout);
    }

    /* 应用默认流量限制 */
    if (!hstate.max_octets_set && default_max_octets > 0) {
        #ifdef MAXOCTETS
        maxoctets = default_max_octets;
        maxoctets_dir = default_max_octets_dir;
        info("HTTP认证: 应用默认流量限制 %d 字节，方向 %d",
             default_max_octets, default_max_octets_dir);
        #endif
    }

    /* 应用默认MTU */
    if (hstate.framed_mtu == 0 && default_framed_mtu > 0) {
        hstate.framed_mtu = default_framed_mtu;
        /* 设置MTU环境变量，由ip-up脚本处理 */
        char mtu_str[16];
        snprintf(mtu_str, sizeof(mtu_str), "%d", default_framed_mtu);
        script_setenv("HTTP_FRAMED_MTU", mtu_str, 1);
        info("HTTP认证: 应用默认MTU %d", default_framed_mtu);
    }
}

/**********************************************************************
* IP地址授权检查函数 - 参考RADIUS插件
***********************************************************************/
static int httpauth_allowed_address(u_int32_t addr)
{
    /* 如果认证成功且设置了允许任何IP，则授权通过 */
    if (hstate.any_ip_addr_ok) {
        info("HTTP认证: IP地址 %I 已授权", htonl(addr));
        return 1;
    }

    /* 如果设置了特定IP地址，检查是否匹配 */
    if (hstate.choose_ip && addr == hstate.ip_addr) {
        info("HTTP认证: 特定IP地址 %I 已授权", htonl(addr));
        return 1;
    }

    /* 默认拒绝 */
    warn("HTTP认证: IP地址 %I 未授权", htonl(addr));
    return 0;
}

/**********************************************************************
* IP地址选择函数 - 参考RADIUS插件
***********************************************************************/
static void httpauth_choose_ip(u_int32_t *addrp)
{
    /* 如果设置了特定IP地址，使用它 */
    if (hstate.choose_ip) {
        *addrp = hstate.ip_addr;
        info("HTTP认证: 为用户 '%s' 选择IP地址 %I", hstate.user, htonl(*addrp));
    }
    /* 否则让xl2tpd从配置的范围中选择 */
}

/**********************************************************************
* 生成唯一的会话ID
***********************************************************************/
static char *generate_session_id(void)
{
    static char session_id[33];
    struct timeval tv;
    unsigned int random_value;

    gettimeofday(&tv, NULL);
    srandom(tv.tv_sec ^ tv.tv_usec);
    random_value = random();

    snprintf(session_id, sizeof(session_id), "%08lx%08lx%08x%08x",
             (unsigned long)tv.tv_sec, (unsigned long)tv.tv_usec,
             getpid(), random_value);

    return session_id;
}

/**********************************************************************
* IP连接建立回调 - 开始计费
***********************************************************************/
static void httpauth_ip_up(void *opaque, int arg)
{
    /* 抑制未使用参数警告 */
    (void)opaque;
    (void)arg;

    if (do_accounting==1) {
        httpauth_acct_start();
    }
}

/**********************************************************************
* IP连接断开回调 - 停止计费
***********************************************************************/
static void httpauth_ip_down(void *opaque, int arg)
{
    /* 抑制未使用参数警告 */
    (void)opaque;
    (void)arg;

    if (do_accounting==1 && hstate.accounting_started) {
        httpauth_acct_stop();
    }
}

/**********************************************************************
* 开始计费 - 参考RADIUS插件
***********************************************************************/
static void httpauth_acct_start(void)
{
    if (hstate.accounting_started) {
        return;  /* 已经开始计费 */
    }

    /* 生成会话ID */
    strncpy(hstate.session_id, generate_session_id(), sizeof(hstate.session_id) - 1);
    hstate.session_id[sizeof(hstate.session_id) - 1] = '\0';

    /* 记录开始时间 */
    hstate.start_time = time(NULL);

    /* 获取初始统计数据 */
    hstate.bytes_in_start = link_stats.bytes_in;
    hstate.bytes_out_start = link_stats.bytes_out;
    hstate.pkts_in_start = link_stats.pkts_in;
    hstate.pkts_out_start = link_stats.pkts_out;

    /* 发送开始计费请求 */
    if (send_http_accounting("Start", hstate.user, 0, 0, 0, 0, 0)) {
        hstate.accounting_started = 1;
        info("HTTP计费: 用户 '%s' 开始计费，会话ID: %s", hstate.user, hstate.session_id);

        /* 设置中间计费定时器 */
        if (acct_interim_interval > 0) {
            timeout(httpauth_acct_interim, NULL, acct_interim_interval, 0);
        }
    } else {
        warn("HTTP计费: 开始计费失败");
    }
}

/**********************************************************************
* 停止计费 - 参考RADIUS插件
***********************************************************************/
static void httpauth_acct_stop(void)
{
    time_t session_time;
    u_int32_t bytes_in, bytes_out, pkts_in, pkts_out;

    if (!hstate.accounting_started) {
        return;  /* 计费未开始 */
    }

    /* 取消中间计费定时器 */
    if (acct_interim_interval > 0) {
        untimeout(httpauth_acct_interim, NULL);
    }

    /* 更新链路统计数据 - 参考RADIUS插件 */
    update_link_stats(0);

    /* 计算会话时间和流量 */
    session_time = time(NULL) - hstate.start_time;

    if (link_stats_valid) {
        bytes_in = link_stats.bytes_in - hstate.bytes_in_start;
        bytes_out = link_stats.bytes_out - hstate.bytes_out_start;
        pkts_in = link_stats.pkts_in - hstate.pkts_in_start;
        pkts_out = link_stats.pkts_out - hstate.pkts_out_start;
    } else {
        /* 如果统计数据无效，使用0值 */
        bytes_in = bytes_out = pkts_in = pkts_out = 0;
        warn("HTTP计费: 链路统计数据无效，使用0值");
    }

    /* 发送停止计费请求 */
    if (send_http_accounting("Stop", hstate.user, session_time,
                           bytes_in, bytes_out, pkts_in, pkts_out)) {
        info("HTTP计费: 用户 '%s' 停止计费，会话时间: %ld秒，流量: %u/%u字节",
             hstate.user, session_time, bytes_in, bytes_out);
    } else {
        warn("HTTP计费: 停止计费失败");
    }

    hstate.accounting_started = 0;
}

/**********************************************************************
* 中间计费 - 参考RADIUS插件
***********************************************************************/
static void httpauth_acct_interim(void *ignored)
{
    time_t session_time;
    u_int32_t bytes_in, bytes_out, pkts_in, pkts_out;

    /* 抑制未使用参数警告 */
    (void)ignored;

    if (!hstate.accounting_started) {
        return;  /* 计费未开始 */
    }

    /* 更新链路统计数据 - 参考RADIUS插件 */
    update_link_stats(0);

    /* 计算当前会话时间和流量 */
    session_time = time(NULL) - hstate.start_time;

    if (link_stats_valid) {
        bytes_in = link_stats.bytes_in - hstate.bytes_in_start;
        bytes_out = link_stats.bytes_out - hstate.bytes_out_start;
        pkts_in = link_stats.pkts_in - hstate.pkts_in_start;
        pkts_out = link_stats.pkts_out - hstate.pkts_out_start;

        /* 重置link_stats_valid标志，强制下次更新 */
        link_stats_valid = 0;
    } else {
        /* 如果统计数据无效，使用0值 */
        bytes_in = bytes_out = pkts_in = pkts_out = 0;
        warn("HTTP计费: 链路统计数据无效，使用0值");
    }

    /* 记录调试信息 */
    info("HTTP计费调试: 当前统计 - bytes_in=%u, bytes_out=%u, pkts_in=%u, pkts_out=%u",
         link_stats.bytes_in, link_stats.bytes_out, link_stats.pkts_in, link_stats.pkts_out);
    info("HTTP计费调试: 开始统计 - bytes_in_start=%u, bytes_out_start=%u, pkts_in_start=%u, pkts_out_start=%u",
         hstate.bytes_in_start, hstate.bytes_out_start, hstate.pkts_in_start, hstate.pkts_out_start);
    info("HTTP计费调试: 差值统计 - bytes_in=%u, bytes_out=%u, pkts_in=%u, pkts_out=%u",
         bytes_in, bytes_out, pkts_in, pkts_out);

    /* 发送中间计费请求 */
    if (send_http_accounting("Interim-Update", hstate.user, session_time,
                           bytes_in, bytes_out, pkts_in, pkts_out)) {
        info("HTTP计费: 用户 '%s' 中间计费，会话时间: %ld秒，流量: %u/%u字节",
             hstate.user, session_time, bytes_in, bytes_out);
    } else {
        warn("HTTP计费: 中间计费失败");
    }

    /* 重新设置定时器 */
    timeout(httpauth_acct_interim, NULL, acct_interim_interval, 0);
}

/**********************************************************************
* 发送HTTP计费请求 - 参考RADIUS插件的计费格式
***********************************************************************/
static int send_http_accounting(const char *acct_type, const char *username,
                               time_t session_time, u_int32_t bytes_in, u_int32_t bytes_out,
                               u_int32_t pkts_in, u_int32_t pkts_out)
{
    CURL *curl;
    CURLcode res;
    struct http_response response = {0};
    char post_data[1024];
    json_object *json_resp, *status_obj;
    int result = 0;
    time_t timestamp = time(NULL);
    char *signature;
    char sign_input[512];

    curl = curl_easy_init();
    if (!curl) {
        error("HTTP计费: 初始化curl失败");
        return 0;
    }

    /* 准备签名输入 */
    snprintf(sign_input, sizeof(sign_input), "%s%s%s%ld%ld%u%u%u%u%ld%s",
             acct_type, username, hstate.session_id, session_time,
             timestamp, bytes_in, bytes_out, pkts_in, pkts_out,
             timestamp, sign_key);

    signature = calculate_md5(sign_input);
    if (!signature) {
        error("HTTP计费: 计算签名失败");
        curl_easy_cleanup(curl);
        return 0;
    }

    /* 准备POST数据 - 计费格式 */
    snprintf(post_data, sizeof(post_data),
             "acct_type=%s&u=%s&session_id=%s&session_time=%ld&"
             "bytes_in=%u&bytes_out=%u&pkts_in=%u&pkts_out=%u&"
             "t=%ld&s=%s",
             acct_type, username, hstate.session_id, session_time,
             bytes_in, bytes_out, pkts_in, pkts_out,
             timestamp, signature);

    /* 设置curl选项 */
    curl_easy_setopt(curl, CURLOPT_URL, acct_url);
    curl_easy_setopt(curl, CURLOPT_POSTFIELDS, post_data);
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, write_callback);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 30L);
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);

    /* 执行请求 */
    res = curl_easy_perform(curl);
    curl_easy_cleanup(curl);
    free(signature);

    if (res != CURLE_OK) {
        error("HTTP计费: 请求失败: %s", curl_easy_strerror(res));
        if (response.data) free(response.data);
        return 0;
    }

    if (!response.data) {
        error("HTTP计费: 服务器响应为空");
        return 0;
    }

    /* 解析JSON响应 */
    json_resp = json_tokener_parse(response.data);
    if (!json_resp) {
        error("HTTP计费: 无效的JSON响应");
        free(response.data);
        return 0;
    }

    /* 检查状态 */
    if (json_object_object_get_ex(json_resp, "status", &status_obj)) {
        const char *status = json_object_get_string(status_obj);
        if (status && strcmp(status, "ok") == 0) {
            result = 1;
        }
    }

    /* 清理 */
    json_object_put(json_resp);
    free(response.data);

    return result;
}
