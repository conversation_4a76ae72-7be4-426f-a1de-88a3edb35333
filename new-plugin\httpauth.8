.\" Manual page for httpauth plugin
.TH HTTPAUTH 8 "1 Jan 2024"
.SH NAME
httpauth \- HTTP URL authentication plugin for pppd
.SH SYNOPSIS
.B pppd
[
.I options
]
.B plugin httpauth.so
[
.I httpauth-options
]
.SH DESCRIPTION
The
.B httpauth
plugin for
.BR pppd (8)
performs authentication of peers via HTTP API calls instead of using
local password files or RADIUS servers.

When a peer attempts to authenticate, the plugin sends an HTTP POST request
to the configured authentication URL with the username and password parameters.
The server response is expected to be in JSON format containing authentication
result and signature verification data.

.SH OPTIONS
The
.B httpauth
plugin supports the following options:

.TP
.BI "auth-url " url
Specifies the HTTP URL to use for authentication requests.
The default is "https://testapi.softapi.cn/notify/pcm_ok".

.TP
.BI "sign-key " key
Specifies the signature key used for response verification.
The default is "hYC0ztcOKp2aZ5t0".

.SH AUTHENTICATION PROTOCOL
The plugin sends HTTP POST requests with the following parameters:
.IP \(bu 4
.B username
- The username provided by the peer
.IP \(bu 4
.B password
- The password provided by the peer

The server is expected to respond with JSON in the following format:
.nf
{
    "pass": 1,
    "t": 1752454664,
    "s": "673261d844d929b52a468404aac290ca"
}
.fi

Where:
.IP \(bu 4
.B pass
- Authentication result (1 = success, other values = failure)
.IP \(bu 4
.B t
- Server timestamp
.IP \(bu 4
.B s
- MD5 signature calculated as md5(username + password + t + sign_key)

.SH SIGNATURE VERIFICATION
The plugin verifies the server response by calculating the expected signature
using the formula:
.nf
md5(username + password + timestamp + sign_key)
.fi

If the calculated signature matches the one provided by the server, and the
pass value is 1, authentication succeeds.

.SH EXAMPLES
To use the httpauth plugin with default settings:
.nf
pppd plugin httpauth.so
.fi

To specify a custom authentication URL:
.nf
pppd plugin httpauth.so auth-url https://myserver.com/auth
.fi

To specify a custom signature key:
.nf
pppd plugin httpauth.so sign-key mySecretKey123
.fi

.SH DEPENDENCIES
This plugin requires the following libraries:
.IP \(bu 4
libcurl - for HTTP requests
.IP \(bu 4
libjson-c - for JSON parsing
.IP \(bu 4
libssl/libcrypto - for MD5 calculation

.SH FILES
.TP
.I /usr/lib/pppd/*/httpauth.so
The plugin shared library

.SH SEE ALSO
.BR pppd (8),
.BR pppd-radius (8)

.SH AUTHOR
HTTP Authentication Plugin

.SH BUGS
CHAP authentication currently uses a simplified implementation.
For production use, proper CHAP challenge-response verification
should be implemented on the server side.
