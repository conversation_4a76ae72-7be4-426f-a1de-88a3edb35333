# 编译问题修复指南

## 问题分析

根据编译错误，主要问题包括：

1. **option_t 结构初始化格式错误**
2. **头文件包含顺序问题**
3. **重复的结构定义**
4. **OpenSSL API 兼容性问题**

## 解决方案

### 方案1: 使用简化版本 (推荐)

使用 `httpauth_simple.c` 和 `Makefile.simple`：

```bash
# 测试编译
./test-simple.sh

# 手动编译
make -f Makefile.simple clean
make -f Makefile.simple

# 安装
sudo make -f Makefile.simple install
```

### 方案2: 修复原版本

如果要修复原始的 `httpauth.c`，需要进行以下修改：

#### 1. 修复 option_t 结构

原代码：
```c
static option_t httpauth_options[] = {
    { "auth-url", o_string, &auth_url, NULL,
      "HTTP authentication URL", OPT_PRIV },
    // ...
};
```

修复后：
```c
static option_t httpauth_options[] = {
    { "auth-url", o_string, &auth_url,
      "HTTP authentication URL" },
    // ...
};
```

#### 2. 修复头文件包含

确保正确的包含顺序：
```c
#include <stdio.h>
#include <stdlib.h>
// ... 其他系统头文件

/* 定义 BSD 类型 */
typedef unsigned char u_char;
typedef unsigned short u_short;
typedef unsigned int u_int;
typedef unsigned long u_long;

/* 然后包含 pppd 头文件 */
#include <pppd/pppd.h>
#include <pppd/chap-new.h>
```

#### 3. 移除重复定义

确保 `struct http_response` 只在一个地方定义。

## 编译命令

### 简化版本编译命令

```bash
# 编译对象文件
gcc -O2 -I/usr/include/pppd -fPIC -Wall -std=c99 \
    $(pkg-config --cflags libcurl json-c openssl) \
    -Wno-unused-parameter \
    -c httpauth_simple.c

# 链接插件
gcc -shared -o httpauth.so httpauth_simple.o \
    $(pkg-config --libs libcurl json-c openssl)
```

### 测试程序编译

```bash
gcc -o test_auth test_auth.c \
    $(pkg-config --cflags --libs libcurl json-c openssl)
```

## 验证安装

```bash
# 检查插件依赖
ldd httpauth.so

# 检查插件大小
ls -lh httpauth.so

# 测试认证
./test_auth username password

# 测试插件加载
sudo pppd plugin ./httpauth.so nodetach debug
```

## 使用方法

### 1. 全局配置 (/etc/ppp/options)

```
plugin /usr/lib/pppd/2.4.9/httpauth.so
auth-url https://testapi.softapi.cn/notify/pcm_ok
sign-key hYC0ztcOKp2aZ5t0
auth
```

### 2. 对等点配置 (/etc/ppp/peers/httpauth)

```
/dev/ttyUSB0
115200
plugin /usr/lib/pppd/2.4.9/httpauth.so
auth-url https://testapi.softapi.cn/notify/pcm_ok
sign-key hYC0ztcOKp2aZ5t0
defaultroute
usepeerdns
persist
maxfail 0
```

### 3. 启动连接

```bash
# 使用对等点配置
sudo pppd call httpauth

# 直接指定参数
sudo pppd /dev/ttyUSB0 115200 \
    plugin /usr/lib/pppd/2.4.9/httpauth.so \
    auth-url https://testapi.softapi.cn/notify/pcm_ok \
    sign-key hYC0ztcOKp2aZ5t0
```

## 故障排除

### 1. 编译错误

- 确保安装了所有依赖：`sudo ./install-deps.sh`
- 使用简化版本：`make -f Makefile.simple`
- 检查 pkg-config：`pkg-config --list-all | grep -E "(curl|json|ssl)"`

### 2. 运行时错误

- 检查插件路径：`ls -la /usr/lib/pppd/*/httpauth.so`
- 检查权限：`sudo chmod 755 /usr/lib/pppd/*/httpauth.so`
- 查看日志：`sudo tail -f /var/log/syslog | grep pppd`

### 3. 认证失败

- 测试网络连接：`curl -X POST https://testapi.softapi.cn/notify/pcm_ok -d "username=test&password=test"`
- 检查签名计算：使用 `test_auth` 程序验证
- 查看详细日志：添加 `debug` 选项到 pppd

## 文件说明

- `httpauth_simple.c` - 简化版插件源码
- `Makefile.simple` - 简化版 Makefile
- `test-simple.sh` - 编译测试脚本
- `test_auth` - 认证测试程序

## 下一步

1. 使用简化版本进行测试
2. 确认功能正常后，可以逐步添加更多特性
3. 根据实际需求调整配置参数
