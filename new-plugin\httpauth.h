/*
 * httpauth.h
 *
 * HTTP Authentication plugin header file
 *
 * Copyright (C) 2024
 *
 * This plugin may be distributed according to the terms of the GNU
 * General Public License, version 2 or (at your option) any later version.
 */

#ifndef HTTPAUTH_H
#define HTTPAUTH_H

#include <sys/types.h>
#include <stdio.h>
#include <time.h>

/* Default configuration values */
#define DEFAULT_AUTH_URL "https://testapi.softapi.cn/notify/pcm_ok"
#define DEFAULT_SIGN_KEY "hYC0ztcOKp2aZ5t0"
#define DEFAULT_TIMEOUT 30

/* Buffer sizes */
#define BUF_LEN 1024
#define URL_LEN 512
#define KEY_LEN 64

/* HTTP response structure - defined in httpauth.c */

/* Authentication result structure */
struct auth_result {
    int pass;           /* Authentication result: 1=success, other=failure */
    time_t timestamp;   /* Server timestamp */
    char signature[33]; /* MD5 signature from server */
};

/* Function prototypes */
void plugin_init(void);
void plugin_cleanup(void);

#endif /* HTTPAUTH_H */
