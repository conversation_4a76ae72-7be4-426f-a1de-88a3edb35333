#!/bin/bash

# Quick installation script without hanging tests
# Ubuntu 24.04

set -e

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    print_error "This script needs to be run with sudo"
    echo "Usage: sudo ./quick-install.sh"
    exit 1
fi

print_header "Quick HTTP Authentication Plugin Installation"
echo "System: $(lsb_release -d 2>/dev/null | cut -f2 || echo 'Ubuntu 24.04')"
echo ""

# Step 1: Build and install plugin
print_header "Step 1: Building and Installing Plugin"

echo "Cleaning previous builds..."
make clean >/dev/null 2>&1

echo "Building plugin..."
if make >/dev/null 2>&1; then
    print_success "Plugin built successfully"
else
    print_error "Plugin build failed"
    exit 1
fi

echo "Installing plugin..."
if make install >/dev/null 2>&1; then
    print_success "Plugin installed"
else
    print_error "Plugin installation failed"
    exit 1
fi

# Step 2: Build and install test program
print_header "Step 2: Installing Test Program"

echo "Building test program..."
if make test >/dev/null 2>&1; then
    cp test_auth /usr/local/bin/
    chmod 755 /usr/local/bin/test_auth
    print_success "Test program installed"
else
    print_warning "Test program build failed"
fi

# Step 3: Create configuration files
print_header "Step 3: Creating Configuration Files"

PPPD_VERSION=$(pppd --version 2>&1 | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+' | head -1 || echo "2.4.9")
PLUGIN_PATH="/usr/lib/pppd/$PPPD_VERSION/httpauth.so"

# Create xl2tpd options file
cat > /etc/ppp/options.xl2tpd << EOF
# PPP options for xl2tpd with HTTP authentication
require-pap
refuse-chap
refuse-mschap
refuse-mschap-v2
refuse-eap

# HTTP Authentication Plugin
plugin $PLUGIN_PATH
auth-url https://testapi.softapi.cn/notify/pcm_ok
sign-key hYC0ztcOKp2aZ5t0

# Network settings
proxyarp
nodefaultroute
lock
nobsdcomp
nodeflate
noipdefault

# DNS settings
ms-dns *******
ms-dns *******

# Connection settings
lcp-echo-interval 30
lcp-echo-failure 4
idle 1800
logfile /var/log/xl2tpd.log
EOF

print_success "Created /etc/ppp/options.xl2tpd"

# Create xl2tpd configuration example
mkdir -p /etc/xl2tpd
cat > /etc/xl2tpd/xl2tpd.conf.example << 'EOF'
[global]
listen-addr = 0.0.0.0
port = 1701

[lns default]
ip range = *************00-***************
local ip = *************
require chap = no
refuse pap = no
require authentication = yes
name = HTTPAuthVPNServer
pppoptfile = /etc/ppp/options.xl2tpd
length bit = yes
EOF

print_success "Created /etc/xl2tpd/xl2tpd.conf.example"

# Step 4: Quick verification
print_header "Step 4: Quick Verification"

if [ -f "$PLUGIN_PATH" ]; then
    print_success "Plugin installed: $PLUGIN_PATH"
else
    print_error "Plugin installation failed"
    exit 1
fi

if [ -f "/usr/local/bin/test_auth" ]; then
    print_success "Test program available: test_auth"
else
    print_warning "Test program not available"
fi

if [ -f "/etc/ppp/options.xl2tpd" ]; then
    print_success "PPP configuration created"
else
    print_error "PPP configuration failed"
fi

# Step 5: Display completion message
print_header "Installation Complete!"

echo ""
echo -e "${GREEN}HTTP Authentication Plugin successfully installed!${NC}"
echo ""
echo "📁 Files installed:"
echo "   Plugin: $PLUGIN_PATH"
echo "   Test program: /usr/local/bin/test_auth"
echo "   PPP config: /etc/ppp/options.xl2tpd"
echo "   xl2tpd example: /etc/xl2tpd/xl2tpd.conf.example"
echo ""
echo "🚀 Next steps:"
echo ""
echo "1. Test authentication:"
echo "   test_auth testuser testpass"
echo ""
echo "2. Configure xl2tpd:"
echo "   sudo cp /etc/xl2tpd/xl2tpd.conf.example /etc/xl2tpd/xl2tpd.conf"
echo "   sudo nano /etc/xl2tpd/xl2tpd.conf"
echo ""
echo "3. Start xl2tpd service:"
echo "   sudo systemctl start xl2tpd"
echo "   sudo systemctl enable xl2tpd"
echo ""
echo "4. Verify installation:"
echo "   ./verify.sh"
echo ""
echo "5. Monitor logs:"
echo "   sudo tail -f /var/log/xl2tpd.log"
echo ""
echo "🔧 Configuration:"
echo "   - Edit /etc/ppp/options.xl2tpd for PPP settings"
echo "   - Edit /etc/xl2tpd/xl2tpd.conf for L2TP settings"
echo "   - Modify auth-url and sign-key as needed"
echo ""

print_success "Quick installation completed!"

echo ""
echo "The installation script that was hanging has been fixed."
echo "You can now use the plugin with xl2tpd L2TP VPN server."
