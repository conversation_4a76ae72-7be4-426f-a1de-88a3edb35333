#!/bin/bash

# Install dependencies for HTTP Authentication Plugin on Ubuntu 24.04
# Copyright 2024

set -e

echo "=== Installing Dependencies for HTTP Authentication Plugin ==="
echo "System: Ubuntu 24.04"
echo "Architecture: $(uname -m)"

# Check if running as root or with sudo
if [ "$EUID" -ne 0 ]; then
    echo "This script needs to be run with sudo privileges"
    echo "Usage: sudo ./install-deps.sh"
    exit 1
fi

# Update package list
echo "Updating package list..."
apt-get update

# Install PPP development headers
echo "Installing PPP development package..."
apt-get install -y ppp-dev

# Install libcurl development package
echo "Installing libcurl development package..."
apt-get install -y libcurl4-openssl-dev

# Install JSON-C development package
echo "Installing JSON-C development package..."
apt-get install -y libjson-c-dev

# Install OpenSSL development package
echo "Installing OpenSSL development package..."
apt-get install -y libssl-dev

# Install build tools if not present
echo "Installing build tools..."
apt-get install -y build-essential pkg-config

# Install additional useful packages
echo "Installing additional packages..."
apt-get install -y curl wget

# Verify installations
echo ""
echo "=== Verifying installations ==="

# Check pppd headers
if [ -d "/usr/include/pppd" ]; then
    echo "✓ PPP headers: /usr/include/pppd"
    ls -la /usr/include/pppd/ | head -5
else
    echo "✗ PPP headers not found"
fi

# Check pkg-config packages
echo ""
echo "Checking pkg-config packages:"

if pkg-config --exists libcurl; then
    echo "✓ libcurl: $(pkg-config --modversion libcurl)"
else
    echo "✗ libcurl not found"
fi

if pkg-config --exists json-c; then
    echo "✓ json-c: $(pkg-config --modversion json-c)"
else
    echo "✗ json-c not found"
fi

if pkg-config --exists openssl; then
    echo "✓ openssl: $(pkg-config --modversion openssl)"
else
    echo "✗ openssl not found"
fi

# Check pppd version
echo ""
echo "PPP daemon information:"
if command -v pppd >/dev/null 2>&1; then
    echo "✓ pppd version: $(pppd --version 2>&1 | head -1)"
    echo "✓ pppd location: $(which pppd)"
else
    echo "✗ pppd not found"
fi

# Check directories
echo ""
echo "PPP directories:"
echo "✓ Config directory: /etc/ppp"
echo "✓ Plugin directory: /usr/lib/pppd/"
ls -la /usr/lib/pppd/ 2>/dev/null || echo "  (Plugin directory will be created during installation)"

echo ""
echo "=== Dependencies installation completed ==="
echo ""
echo "Next steps:"
echo "1. Build the plugin: ./build.sh"
echo "2. Install the plugin: sudo make install"
echo "3. Test authentication: ./test_auth username password"
echo ""
echo "Configuration files:"
echo "- Global PPP options: /etc/ppp/options"
echo "- Peer configurations: /etc/ppp/peers/"
echo "- Authentication secrets: /etc/ppp/pap-secrets, /etc/ppp/chap-secrets"
