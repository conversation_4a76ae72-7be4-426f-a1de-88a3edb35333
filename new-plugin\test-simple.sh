#!/bin/bash

# Test script for simplified HTTP authentication plugin
# Ubuntu 24.04

set -e

echo "=== Testing Simplified HTTP Authentication Plugin ==="

# Check dependencies
echo "Checking dependencies..."
for pkg in libcurl json-c openssl; do
    if pkg-config --exists $pkg; then
        echo "✓ $pkg: $(pkg-config --modversion $pkg)"
    else
        echo "✗ $pkg not found"
        exit 1
    fi
done

# Check pppd headers
if [ -d "/usr/include/pppd" ]; then
    echo "✓ pppd headers found"
else
    echo "✗ pppd headers not found"
    exit 1
fi

echo ""
echo "Building simplified version..."

# Clean and build
make -f Makefile.simple clean
make -f Makefile.simple

if [ $? -eq 0 ]; then
    echo "✓ Plugin compiled successfully"
    echo "Plugin size: $(ls -lh httpauth.so | awk '{print $5}')"
else
    echo "✗ Plugin compilation failed"
    exit 1
fi

echo ""
echo "Testing plugin dependencies..."
ldd httpauth.so

echo ""
echo "Building test program..."
make -f Makefile.simple test

if [ $? -eq 0 ]; then
    echo "✓ Test program compiled successfully"
else
    echo "✗ Test program compilation failed"
    exit 1
fi

echo ""
echo "Testing authentication API..."
if ./test_auth testuser testpass; then
    echo "✓ Authentication test passed"
else
    echo "⚠ Authentication test failed (this may be expected if server is not accessible)"
fi

echo ""
echo "=== Test Summary ==="
echo "Files created:"
ls -la httpauth.so test_auth 2>/dev/null || echo "No files created"

echo ""
echo "To install the plugin:"
echo "  sudo make -f Makefile.simple install"
echo ""
echo "To use with pppd:"
echo "  pppd plugin /usr/lib/pppd/2.4.9/httpauth.so auth-url https://testapi.softapi.cn/notify/pcm_ok"
