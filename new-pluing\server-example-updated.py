#!/usr/bin/env python3
"""
HTTP Authentication Server Example
Updated for u/p parameters

This script handles authentication requests from the HTTP auth plugin
with the updated parameter names: u (username) and p (password)

Usage:
    pip install flask
    python server-example-updated.py
"""

import hashlib
import json
import time
import logging
from flask import Flask, request, jsonify

app = Flask(__name__)

# Configuration
SIGN_KEY = 'hYC0ztcOKp2aZ5t0'
DEBUG_MODE = True

# Setup logging
if DEBUG_MODE:
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
else:
    logger = logging.getLogger(__name__)

# Example user database
VALID_USERS = {
    'testuser': 'testpass',
    'admin': 'admin123',
    'user1': 'password1',
    'demo': 'demo'
}

def authenticate_user(username, password):
    """
    Authenticate user against the user database
    Replace this with your actual authentication logic
    """
    return VALID_USERS.get(username) == password

def calculate_signature(username, password, timestamp, sign_key):
    """
    Calculate MD5 signature: md5(username + password + timestamp + sign_key)
    """
    signature_input = f"{username}{password}{timestamp}{sign_key}"
    return hashlib.md5(signature_input.encode()).hexdigest()

@app.route('/notify/pcm_ok', methods=['POST'])
def authenticate():
    """
    Handle authentication requests
    Updated to use 'u' and 'p' parameters
    """
    # Get parameters (updated parameter names)
    username = request.form.get('u', '')
    password = request.form.get('p', '')
    
    logger.info(f"Authentication request for user: {username}")
    
    # Validate input
    if not username or not password:
        logger.warning("Missing username or password")
        timestamp = int(time.time())
        signature = calculate_signature('error', '', timestamp, SIGN_KEY)
        return jsonify({
            'pass': 0,
            't': timestamp,
            's': signature
        })
    
    # Perform authentication
    authenticated = authenticate_user(username, password)
    
    # Generate timestamp
    timestamp = int(time.time())
    
    # Calculate signature
    signature = calculate_signature(username, password, timestamp, SIGN_KEY)
    
    logger.info(f"Authentication result for {username}: {'SUCCESS' if authenticated else 'FAILED'}")
    logger.info(f"Signature input: {username}{password}{timestamp}{SIGN_KEY}")
    logger.info(f"Calculated signature: {signature}")
    
    # Prepare response
    response = {
        'pass': 1 if authenticated else 0,
        't': timestamp,
        's': signature
    }
    
    # Add debug information if enabled
    if DEBUG_MODE:
        response['debug'] = {
            'username': username,
            'timestamp': timestamp,
            'signature_input': f"{username}{password}{timestamp}{SIGN_KEY}",
            'auth_result': 'success' if authenticated else 'failed'
        }
    
    logger.info(f"Response: {json.dumps(response)}")
    return jsonify(response)

@app.route('/test', methods=['GET'])
def test():
    """
    Test endpoint to verify server is running
    """
    return jsonify({
        'status': 'ok',
        'message': 'HTTP Authentication Server is running',
        'parameters': 'u (username), p (password)',
        'timestamp': int(time.time())
    })

@app.route('/', methods=['GET'])
def index():
    """
    Index page with usage information
    """
    return """
    <h1>HTTP Authentication Server</h1>
    <h2>Updated for u/p parameters</h2>
    
    <h3>Usage:</h3>
    <p>POST to /notify/pcm_ok with parameters:</p>
    <ul>
        <li><strong>u</strong>: username</li>
        <li><strong>p</strong>: password</li>
    </ul>
    
    <h3>Response Format:</h3>
    <pre>{
    "pass": 1,
    "t": 1752454664,
    "s": "md5_signature"
}</pre>
    
    <h3>Test Users:</h3>
    <ul>
        <li>testuser / testpass</li>
        <li>admin / admin123</li>
        <li>user1 / password1</li>
        <li>demo / demo</li>
    </ul>
    
    <h3>Test Command:</h3>
    <pre>curl -X POST http://localhost:5000/notify/pcm_ok -d "u=testuser&p=testpass"</pre>
    """

if __name__ == '__main__':
    print("Starting HTTP Authentication Server...")
    print("Updated for u/p parameters")
    print("Test users: testuser/testpass, admin/admin123, user1/password1, demo/demo")
    print("Test URL: http://localhost:5000/notify/pcm_ok")
    print("Test command: curl -X POST http://localhost:5000/notify/pcm_ok -d \"u=testuser&p=testpass\"")
    
    app.run(host='0.0.0.0', port=5000, debug=DEBUG_MODE)
