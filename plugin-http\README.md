# HTTP Authentication Plugin for PPP/L2TP

这是一个用于PPP守护进程的HTTP认证插件，特别适用于xl2tpd L2TP VPN服务器。插件通过HTTP API进行用户认证，支持签名验证以确保安全性。

## 特性

- **HTTP API认证** - 通过HTTP POST请求进行用户验证
- **签名验证** - MD5签名确保响应完整性
- **xl2tpd集成** - 专为xl2tpd L2TP服务器优化
- **简洁参数** - 使用u/p参数减少网络传输
- **Ubuntu 24.04优化** - 完全兼容最新Ubuntu LTS

## 系统要求

- Ubuntu 24.04 LTS
- pppd 2.4.9+
- xl2tpd
- libcurl4-openssl-dev
- libjson-c-dev
- libssl-dev

## 快速安装

```bash
# 克隆或下载插件文件
cd plugin-http

# 运行安装脚本
sudo ./install.sh
```

安装脚本将自动：
1. 安装所有依赖包
2. 编译并安装插件
3. 创建配置文件
4. 安装测试程序

## 手动安装

```bash
# 安装依赖
sudo apt-get update
sudo apt-get install ppp-dev libcurl4-openssl-dev libjson-c-dev libssl-dev build-essential pkg-config xl2tpd

# 编译插件
make clean
make

# 安装插件
sudo make install

# 测试程序
make test
sudo cp test_auth /usr/local/bin/
```

## 配置

### 1. PPP配置 (/etc/ppp/options.xl2tpd)

```
# 认证设置
require-pap
refuse-chap
refuse-mschap
refuse-mschap-v2
refuse-eap

# HTTP认证插件
plugin /usr/lib/pppd/2.4.9/httpauth.so
auth-url https://testapi.softapi.cn/notify/pcm_ok
sign-key hYC0ztcOKp2aZ5t0

# 网络设置
proxyarp
nodefaultroute
lock
nobsdcomp
nodeflate
noipdefault

# DNS设置
ms-dns *******
ms-dns *******

# 连接设置
lcp-echo-interval 30
lcp-echo-failure 4
idle 1800
logfile /var/log/xl2tpd.log
```

### 2. xl2tpd配置 (/etc/xl2tpd/xl2tpd.conf)

```ini
[global]
listen-addr = 0.0.0.0
port = 1701

[lns default]
ip range = *************00-***************
local ip = *************
require chap = no
refuse pap = no
require authentication = yes
name = HTTPAuthVPNServer
pppoptfile = /etc/ppp/options.xl2tpd
length bit = yes
```

## API接口

### 请求格式

```
POST https://testapi.softapi.cn/notify/pcm_ok
Content-Type: application/x-www-form-urlencoded

u=用户名&p=密码
```

### 响应格式

```json
{
    "pass": 1,
    "t": 1752454664,
    "s": "673261d844d929b52a468404aac290ca"
}
```

### 字段说明

- `pass`: 认证结果 (1=成功, 其他=失败)
- `t`: 服务器时间戳
- `s`: MD5签名 = md5(username + password + timestamp + sign_key)

## 服务器端实现

### PHP示例

```php
<?php
header('Content-Type: application/json');

$username = $_POST['u'] ?? '';
$password = $_POST['p'] ?? '';
$sign_key = 'hYC0ztcOKp2aZ5t0';

// 你的认证逻辑
$authenticated = authenticate_user($username, $password);

$timestamp = time();
$signature = md5($username . $password . $timestamp . $sign_key);

echo json_encode([
    'pass' => $authenticated ? 1 : 0,
    't' => $timestamp,
    's' => $signature
]);
?>
```

### Python Flask示例

```python
from flask import Flask, request, jsonify
import hashlib
import time

app = Flask(__name__)
SIGN_KEY = 'hYC0ztcOKp2aZ5t0'

@app.route('/notify/pcm_ok', methods=['POST'])
def authenticate():
    username = request.form.get('u', '')
    password = request.form.get('p', '')
    
    # 你的认证逻辑
    authenticated = check_user(username, password)
    
    timestamp = int(time.time())
    signature_input = f"{username}{password}{timestamp}{SIGN_KEY}"
    signature = hashlib.md5(signature_input.encode()).hexdigest()
    
    return jsonify({
        'pass': 1 if authenticated else 0,
        't': timestamp,
        's': signature
    })
```

## 使用方法

### 1. 启动xl2tpd服务

```bash
# 启动服务
sudo systemctl start xl2tpd

# 开机自启
sudo systemctl enable xl2tpd

# 查看状态
sudo systemctl status xl2tpd
```

### 2. 测试认证

```bash
# 测试HTTP认证API
test_auth username password

# 测试PPP插件加载
sudo pppd plugin /usr/lib/pppd/2.4.9/httpauth.so nodetach debug
```

### 3. 客户端连接

客户端配置L2TP VPN连接：
- 服务器地址：你的服务器IP
- 用户名：通过HTTP API验证的用户名
- 密码：对应的密码
- 协议：L2TP/IPSec (可选)

## 故障排除

### 1. 查看日志

```bash
# xl2tpd日志
sudo tail -f /var/log/xl2tpd.log

# 系统日志
sudo journalctl -f | grep -E "(xl2tpd|pppd)"

# PPP调试日志
sudo tail -f /var/log/ppp.log
```

### 2. 常见问题

**插件加载失败**
```bash
# 检查插件是否存在
ls -la /usr/lib/pppd/*/httpauth.so

# 检查依赖
ldd /usr/lib/pppd/2.4.9/httpauth.so
```

**认证失败**
```bash
# 测试API连接
curl -X POST https://testapi.softapi.cn/notify/pcm_ok -d "u=test&p=test"

# 检查签名计算
test_auth username password
```

**连接问题**
```bash
# 检查xl2tpd状态
sudo systemctl status xl2tpd

# 检查端口监听
sudo netstat -ulnp | grep 1701

# 检查防火墙
sudo ufw status
```

### 3. 调试模式

在`/etc/ppp/options.xl2tpd`中添加：
```
debug
logfile /var/log/ppp-debug.log
```

## 安全注意事项

1. **使用HTTPS** - 生产环境必须使用HTTPS
2. **保护签名密钥** - sign-key应该保密并定期更换
3. **防火墙配置** - 限制L2TP端口(1701)的访问
4. **日志监控** - 监控认证失败和异常连接
5. **IPSec加密** - 考虑启用IPSec提供额外安全层

## 文件说明

- `httpauth.c` - 插件源码
- `Makefile` - 编译配置
- `test_auth.c` - 测试程序
- `install.sh` - 安装脚本
- `options.xl2tpd.example` - PPP配置示例
- `xl2tpd.conf.example` - xl2tpd配置示例

## 支持

- 查看日志文件排查问题
- 使用test_auth程序测试API
- 检查网络连接和防火墙设置
- 验证服务器端API实现

## 许可证

本插件基于GNU General Public License v2发布。
