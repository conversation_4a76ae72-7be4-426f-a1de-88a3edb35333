;
; xl2tpd configuration for HTTP authentication
; /etc/xl2tpd/xl2tpd.conf
;
; This configuration sets up xl2tpd to work with the HTTP authentication plugin
; Copy this file to /etc/xl2tpd/xl2tpd.conf and modify as needed
;

; ============================================================================
; Global Settings
; ============================================================================

[global]
; IP address to listen on (0.0.0.0 = all interfaces)
listen-addr = 0.0.0.0

; L2TP port (standard is 1701)
port = 1701

; Access control (not used with HTTP auth)
; auth file = /etc/xl2tpd/l2tp-secrets

; Maximum number of concurrent connections
max connections = 100

; Debug level (0=none, 1=basic, 2=verbose)
debug avp = no
debug network = no
debug packet = no
debug state = no
debug tunnel = no

; ============================================================================
; LNS (L2TP Network Server) Configuration
; ============================================================================

[lns default]
; Server name
name = HTTPAuthVPNServer

; IP range for client assignment
ip range = ***************-***************

; Local IP (server IP in the VPN network)
local ip = *************

; Authentication settings
require chap = no
refuse pap = no
require authentication = yes

; Use HTTP authentication instead of traditional methods
; The actual authentication is handled by the HTTP plugin in PPP options

; PPP options file
pppoptfile = /etc/ppp/options.xl2tpd

; Enable length bit in L2TP header
length bit = yes

; Challenge authentication
challenge = no

; Exclusive access to PPP
exclusive = no



; ============================================================================
; Advanced LNS Settings
; ============================================================================



; Call serial number start
call serial = 1

; Redial settings
redial = yes
redial timeout = 30
max redials = 5

; ============================================================================
; LAC (L2TP Access Concentrator) Configuration (if needed)
; ============================================================================

; Uncomment if you need LAC functionality
; [lac myisp]
; lns = l2tp.myisp.com
; require pap = yes
; refuse chap = yes
; pppoptfile = /etc/ppp/options.xl2tpd.lac
; redial = yes
; redial timeout = 15
; max redials = 5

; ============================================================================
; Example Configurations for Different Scenarios
; ============================================================================

; High-capacity server configuration
[lns high-capacity]
name = HighCapacityVPN
ip range = **********-**********
local ip = ********
require authentication = yes
pppoptfile = /etc/ppp/options.xl2tpd
length bit = yes
max sessions = 50

; Secure configuration
[lns secure]
name = SecureVPN
ip range = ************-************
local ip = **********
require authentication = yes
require chap = no
refuse pap = no
pppoptfile = /etc/ppp/options.xl2tpd.secure
length bit = yes
tunnel authentication = no

; Debug configuration
[lns debug]
name = DebugVPN
ip range = *************00-*************50
local ip = *************
require authentication = yes
pppoptfile = /etc/ppp/options.xl2tpd.debug
length bit = yes
; Enable debugging for this LNS
debug avp = yes
debug network = yes
debug state = yes

; ============================================================================
; Configuration Notes
; ============================================================================

; 1. IP Ranges:
;    - Choose IP ranges that don't conflict with your existing network
;    - Common private ranges: 192.168.x.x, 172.16.x.x, 10.x.x.x
;    - Ensure the range is large enough for your expected users

; 2. Authentication:
;    - HTTP authentication is handled by the PPP plugin
;    - Set "require authentication = yes" to enforce auth
;    - Disable traditional L2TP authentication methods

; 3. PPP Options:
;    - The pppoptfile points to your PPP configuration
;    - This file should contain the HTTP auth plugin settings
;    - Different LNS sections can use different PPP option files

; 4. Security:
;    - Use IPSec for additional security (configure separately)
;    - Monitor logs for unauthorized access attempts
;    - Consider firewall rules to restrict access

; 5. Performance:
;    - Adjust max connections and max sessions based on server capacity
;    - Monitor CPU and memory usage under load
;    - Consider multiple xl2tpd instances for very high loads

; 6. Debugging:
;    - Enable debug options only when troubleshooting
;    - Check /var/log/xl2tpd.log for connection issues
;    - Use tcpdump to capture L2TP traffic if needed

; 7. Testing:
;    - Test with a single client first
;    - Verify IP assignment and routing
;    - Check that HTTP authentication is working
;    - Test under load with multiple concurrent connections
