#!/bin/bash

# Complete installation and setup script for HTTP Authentication Plugin
# Ubuntu 24.04

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    print_error "This script needs to be run with sudo privileges"
    echo "Usage: sudo ./install-and-setup.sh"
    exit 1
fi

print_header "HTTP Authentication Plugin Installation"
echo "System: $(lsb_release -d 2>/dev/null | cut -f2 || echo 'Ubuntu 24.04')"
echo "Date: $(date)"
echo ""

# Step 1: Install dependencies
print_header "Step 1: Installing Dependencies"
apt-get update
apt-get install -y ppp-dev libcurl4-openssl-dev libjson-c-dev libssl-dev build-essential pkg-config

print_success "Dependencies installed"

# Step 2: Build plugin
print_header "Step 2: Building Plugin"
make -f Makefile.simple clean
make -f Makefile.simple

if [ -f "httpauth.so" ]; then
    print_success "Plugin built successfully"
else
    print_error "Plugin build failed"
    exit 1
fi

# Step 3: Install plugin
print_header "Step 3: Installing Plugin"
PPPD_VERSION=$(pppd --version 2>&1 | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+' | head -1 || echo "2.4.9")
PLUGIN_DIR="/usr/lib/pppd/$PPPD_VERSION"

echo "PPP version: $PPPD_VERSION"
echo "Plugin directory: $PLUGIN_DIR"

# Create plugin directory if it doesn't exist
mkdir -p "$PLUGIN_DIR"

# Install plugin
cp httpauth.so "$PLUGIN_DIR/"
chmod 755 "$PLUGIN_DIR/httpauth.so"

print_success "Plugin installed to $PLUGIN_DIR/httpauth.so"

# Step 4: Build test program
print_header "Step 4: Building Test Program"
make -f Makefile.simple test

if [ -f "test_auth" ]; then
    print_success "Test program built"
    cp test_auth /usr/local/bin/
    chmod 755 /usr/local/bin/test_auth
    print_success "Test program installed to /usr/local/bin/test_auth"
else
    print_warning "Test program build failed"
fi

# Step 5: Create configuration examples
print_header "Step 5: Creating Configuration Examples"

# Create example peer configuration
PEER_CONFIG="/etc/ppp/peers/httpauth-example"
cat > "$PEER_CONFIG" << EOF
# HTTP Authentication Example Configuration
# Created by install script

# Serial device (adjust as needed)
/dev/ttyUSB0
115200

# HTTP Authentication Plugin
plugin $PLUGIN_DIR/httpauth.so
auth-url https://testapi.softapi.cn/notify/pcm_ok
sign-key hYC0ztcOKp2aZ5t0

# Connection settings
defaultroute
usepeerdns
persist
maxfail 0

# PPP options
asyncmap 0
auth
crtscts
lock
hide-password

# Uncomment for debugging
# debug
# logfile /tmp/ppp-httpauth.log
EOF

print_success "Example configuration created: $PEER_CONFIG"

# Step 6: Test installation
print_header "Step 6: Testing Installation"

echo "Testing plugin loading..."
if timeout 5 pppd plugin "$PLUGIN_DIR/httpauth.so" nodetach 2>&1 | grep -q "HTTP authentication plugin initialized"; then
    print_success "Plugin loads successfully"
else
    print_warning "Plugin loading test inconclusive"
fi

echo ""
echo "Testing authentication API..."
if [ -f "/usr/local/bin/test_auth" ]; then
    if /usr/local/bin/test_auth testuser testpass >/dev/null 2>&1; then
        print_success "Authentication API test passed"
    else
        print_warning "Authentication API test failed (may be expected if server is unreachable)"
    fi
else
    print_warning "Test program not available"
fi

# Step 7: Display usage information
print_header "Installation Complete!"

echo ""
echo -e "${GREEN}Plugin successfully installed!${NC}"
echo ""
echo "📁 Files installed:"
echo "   Plugin: $PLUGIN_DIR/httpauth.so"
echo "   Test program: /usr/local/bin/test_auth"
echo "   Example config: $PEER_CONFIG"
echo ""
echo "🚀 Quick start:"
echo "   1. Test authentication:"
echo "      test_auth <username> <password>"
echo ""
echo "   2. Edit configuration:"
echo "      nano $PEER_CONFIG"
echo ""
echo "   3. Start PPP connection:"
echo "      pppd call httpauth-example"
echo ""
echo "🔧 Configuration options:"
echo "   auth-url    - Authentication server URL"
echo "   sign-key    - Signature verification key"
echo ""
echo "📋 API format:"
echo "   URL: https://testapi.softapi.cn/notify/pcm_ok"
echo "   Method: POST"
echo "   Parameters: username, password"
echo "   Response: {\"pass\":1,\"t\":timestamp,\"s\":\"signature\"}"
echo ""
echo "🔍 Troubleshooting:"
echo "   View logs: tail -f /var/log/syslog | grep pppd"
echo "   Debug mode: Add 'debug' to peer configuration"
echo "   Test plugin: pppd plugin $PLUGIN_DIR/httpauth.so nodetach debug"
echo ""
echo "📖 Documentation:"
echo "   README.md - Detailed documentation"
echo "   example-config.txt - Configuration examples"
echo "   COMPILE-FIX.md - Compilation troubleshooting"
echo ""

print_success "Installation and setup completed successfully!"

echo ""
echo "Next steps:"
echo "1. Adjust the device path in $PEER_CONFIG if needed"
echo "2. Test with: pppd call httpauth-example"
echo "3. Monitor logs for any issues"
