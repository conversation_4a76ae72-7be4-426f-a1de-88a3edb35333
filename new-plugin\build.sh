#!/bin/bash

# Build script for HTTP Authentication Plugin
# Copyright 2024
# Optimized for Ubuntu 24.04

set -e

echo "=== Building HTTP Authentication Plugin for Ubuntu 24.04 ==="

# System information
echo "System: $(lsb_release -d 2>/dev/null | cut -f2 || echo 'Ubuntu 24.04')"
echo "Architecture: $(uname -m)"

# Check for required dependencies
echo "Checking dependencies..."

# Check for pppd headers
if [ ! -d "/usr/include/pppd" ]; then
    echo "Error: pppd development headers not found"
    echo "Install with: sudo apt-get install ppp-dev"
    exit 1
fi
echo "✓ pppd headers found at /usr/include/pppd"

# Check for libcurl
if ! pkg-config --exists libcurl; then
    echo "Error: libcurl development package not found"
    echo "Install with: sudo apt-get install libcurl4-openssl-dev"
    exit 1
fi
echo "✓ libcurl found: $(pkg-config --modversion libcurl)"

# Check for json-c
if ! pkg-config --exists json-c; then
    echo "Error: json-c development package not found"
    echo "Install with: sudo apt-get install libjson-c-dev"
    exit 1
fi
echo "✓ json-c found: $(pkg-config --modversion json-c)"

# Check for openssl
if ! pkg-config --exists openssl; then
    echo "Error: openssl development package not found"
    echo "Install with: sudo apt-get install libssl-dev"
    exit 1
fi
echo "✓ openssl found: $(pkg-config --modversion openssl)"

echo "All dependencies found."

# Get pppd version for plugin directory
PPPD_VERSION=$(pppd --version 2>&1 | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+' | head -1 || echo "2.4.9")
echo "PPP version detected: $PPPD_VERSION"

# Clean previous builds
echo "Cleaning previous builds..."
make clean 2>/dev/null || true

# Build the plugin
echo "Building plugin..."
make PPPD_VERSION=$PPPD_VERSION

# Build test program
echo "Building test program..."
gcc -o test_auth test_auth.c $(pkg-config --cflags --libs libcurl json-c openssl)

echo "=== Build completed successfully ==="
echo ""
echo "Files created:"
echo "  httpauth.so    - PPP plugin"
echo "  test_auth      - Test program"
echo ""
echo "Plugin will be installed to: /usr/lib/pppd/$PPPD_VERSION/"
echo ""
echo "To install dependencies (if not already installed):"
echo "  sudo apt-get update"
echo "  sudo apt-get install ppp-dev libcurl4-openssl-dev libjson-c-dev libssl-dev"
echo ""
echo "To install the plugin:"
echo "  sudo make install PPPD_VERSION=$PPPD_VERSION"
echo ""
echo "To test authentication:"
echo "  ./test_auth <username> <password>"
echo ""
echo "To use with pppd:"
echo "  pppd plugin httpauth.so [options]"
echo ""
echo "Configuration files:"
echo "  /etc/ppp/options           - Global PPP options"
echo "  /etc/ppp/peers/            - Peer configurations"
