# Simple Makefile for HTTP Authentication plugin
# Ubuntu 24.04

CC = gcc
CFLAGS = -O2 -I/usr/include/pppd -fPIC -Wall -std=c99
CFLAGS += $(shell pkg-config --cflags libcurl json-c openssl)
CFLAGS += -Wno-unused-parameter

LDFLAGS = -shared
LIBS = $(shell pkg-config --libs libcurl json-c openssl)

PLUGIN = httpauth.so

all: $(PLUGIN)

$(PLUGIN): httpauth_simple.o
	@echo "Linking plugin..."
	$(CC) $(LDFLAGS) -o $(PLUGIN) httpauth_simple.o $(LIBS)

httpauth_simple.o: httpauth_simple.c
	@echo "Compiling httpauth_simple.c..."
	$(CC) $(CFLAGS) -c httpauth_simple.c

test: test_auth

test_auth: test_auth.c
	@echo "Building test program..."
	$(CC) -o test_auth test_auth.c $(shell pkg-config --cflags --libs libcurl json-c openssl)

clean:
	rm -f *.o $(PLUGIN) test_auth

install: $(PLUGIN)
	@echo "Installing plugin..."
	sudo cp $(PLUGIN) /usr/lib/pppd/2.4.9/
	@echo "Plugin installed to /usr/lib/pppd/2.4.9/$(PLUGIN)"

.PHONY: all test clean install
