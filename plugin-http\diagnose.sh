#!/bin/bash

# Diagnostic script for HTTP Authentication Plugin issues
# Analyzes the "Peer is not authorized to use remote address" problem

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_header "HTTP Authentication Plugin Diagnostics"
echo "Analyzing the IP authorization issue..."
echo ""

# 1. Check current PPP configuration
print_header "1. PPP Configuration Analysis"

if [ -f "/etc/ppp/options.xl2tpd" ]; then
    print_success "PPP options file exists"
    echo ""
    echo "Current configuration:"
    echo "====================="
    cat /etc/ppp/options.xl2tpd
    echo ""
    
    # Check for problematic settings
    if grep -q "noauth" /etc/ppp/options.xl2tpd; then
        print_success "noauth option present (allows IP assignment)"
    else
        print_error "Missing 'noauth' option - this causes IP authorization failures"
    fi
    
    if grep -q "ipcp-accept" /etc/ppp/options.xl2tpd; then
        print_success "IPCP accept options present"
    else
        print_warning "Missing IPCP accept options"
    fi
else
    print_error "PPP options file not found"
fi

echo ""

# 2. Check xl2tpd configuration
print_header "2. xl2tpd Configuration Analysis"

if [ -f "/etc/xl2tpd/xl2tpd.conf" ]; then
    print_success "xl2tpd configuration exists"
    echo ""
    echo "Current xl2tpd configuration:"
    echo "============================"
    cat /etc/xl2tpd/xl2tpd.conf
    echo ""
    
    # Check IP range
    if grep -q "ip range" /etc/xl2tpd/xl2tpd.conf; then
        IP_RANGE=$(grep "ip range" /etc/xl2tpd/xl2tpd.conf | cut -d'=' -f2 | tr -d ' ')
        print_success "IP range configured: $IP_RANGE"
    else
        print_error "No IP range configured"
    fi
    
    if grep -q "local ip" /etc/xl2tpd/xl2tpd.conf; then
        LOCAL_IP=$(grep "local ip" /etc/xl2tpd/xl2tpd.conf | cut -d'=' -f2 | tr -d ' ')
        print_success "Local IP configured: $LOCAL_IP"
    else
        print_error "No local IP configured"
    fi
else
    print_error "xl2tpd configuration not found"
fi

echo ""

# 3. Check service status
print_header "3. Service Status"

if systemctl is-active xl2tpd >/dev/null 2>&1; then
    print_success "xl2tpd service is running"
else
    print_error "xl2tpd service is not running"
    echo "Start with: sudo systemctl start xl2tpd"
fi

if netstat -ulnp 2>/dev/null | grep -q ":1701"; then
    print_success "xl2tpd is listening on port 1701"
else
    print_warning "xl2tpd may not be listening on port 1701"
fi

echo ""

# 4. Check recent logs
print_header "4. Recent Log Analysis"

echo "Recent xl2tpd logs:"
echo "==================="
if [ -f "/var/log/xl2tpd.log" ]; then
    tail -20 /var/log/xl2tpd.log
else
    echo "No xl2tpd.log found, checking syslog..."
    journalctl -u xl2tpd --no-pager -n 10 2>/dev/null || echo "No xl2tpd logs in journal"
fi

echo ""
echo "Recent PPP logs:"
echo "==============="
journalctl | grep -i ppp | tail -10 2>/dev/null || echo "No recent PPP logs found"

echo ""

# 5. Network configuration check
print_header "5. Network Configuration"

echo "Current IP configuration:"
echo "========================"
ip addr show | grep -E "(inet |UP|DOWN)" | head -20

echo ""
echo "Routing table:"
echo "============="
ip route show | head -10

echo ""

# 6. Problem analysis and recommendations
print_header "6. Problem Analysis"

echo ""
echo "🔍 Based on your log: 'Peer is not authorized to use remote address ***********'"
echo ""
echo "📊 This error typically occurs when:"
echo "   1. PPP doesn't allow the client to use the assigned IP"
echo "   2. Missing 'noauth' option in PPP configuration"
echo "   3. IP range conflicts with existing network"
echo "   4. Missing IPCP accept options"
echo ""

print_header "7. Recommended Solutions"

echo ""
echo "🔧 To fix the IP authorization issue:"
echo ""
echo "1. Run the fix script:"
echo "   sudo ./fix-ip-authorization.sh"
echo ""
echo "2. Or manually add these options to /etc/ppp/options.xl2tpd:"
echo "   noauth"
echo "   ipcp-accept-local"
echo "   ipcp-accept-remote"
echo ""
echo "3. Ensure xl2tpd.conf has proper IP range:"
echo "   ip range = ***********-***********0"
echo "   local ip = **********"
echo ""
echo "4. Restart xl2tpd:"
echo "   sudo systemctl restart xl2tpd"
echo ""
echo "5. Test again and monitor logs:"
echo "   sudo tail -f /var/log/xl2tpd.log"
echo ""

print_header "8. Additional Checks"

echo ""
echo "🌐 Network considerations:"
echo ""
echo "1. Check for IP conflicts:"
echo "   ping ***********"
echo ""
echo "2. Enable IP forwarding (if needed):"
echo "   echo 'net.ipv4.ip_forward=1' | sudo tee -a /etc/sysctl.conf"
echo "   sudo sysctl -p"
echo ""
echo "3. Configure firewall:"
echo "   sudo ufw allow 1701/udp"
echo "   sudo ufw allow from **********/24"
echo ""

echo ""
print_success "Diagnostic completed!"
echo ""
echo "The main issue is likely missing 'noauth' and IPCP options in PPP configuration."
echo "Run 'sudo ./fix-ip-authorization.sh' to apply the recommended fixes."
