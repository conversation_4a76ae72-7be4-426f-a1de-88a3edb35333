/*
 * test_auth.c
 *
 * Test program for HTTP authentication functionality
 *
 * Compile with: gcc -o test_auth test_auth.c -lcurl -ljson-c -lssl -lcrypto
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <curl/curl.h>
#include <json-c/json.h>
#include <openssl/evp.h>

#define SIGN_KEY "hYC0ztcOKp2aZ5t0"
#define AUTH_URL "https://testapi.softapi.cn/notify/pcm_ok"

struct http_response {
    char *data;
    size_t size;
};

static size_t write_callback(void *contents, size_t size, size_t nmemb, struct http_response *response)
{
    size_t realsize = size * nmemb;
    char *ptr = realloc(response->data, response->size + realsize + 1);
    
    if (!ptr) {
        printf("Memory allocation failed\n");
        return 0;
    }
    
    response->data = ptr;
    memcpy(&(response->data[response->size]), contents, realsize);
    response->size += realsize;
    response->data[response->size] = 0;
    
    return realsize;
}

static char *calculate_md5(const char *input)
{
    EVP_MD_CTX *mdctx;
    const EVP_MD *md;
    unsigned char digest[EVP_MAX_MD_SIZE];
    unsigned int digest_len;
    char *hash_string;

    hash_string = malloc(33);
    if (!hash_string) {
        return NULL;
    }

    md = EVP_md5();
    mdctx = EVP_MD_CTX_new();

    if (!mdctx) {
        free(hash_string);
        return NULL;
    }

    EVP_DigestInit_ex(mdctx, md, NULL);
    EVP_DigestUpdate(mdctx, input, strlen(input));
    EVP_DigestFinal_ex(mdctx, digest, &digest_len);
    EVP_MD_CTX_free(mdctx);

    for (unsigned int i = 0; i < digest_len; i++) {
        sprintf(&hash_string[i*2], "%02x", digest[i]);
    }

    return hash_string;
}

static int verify_signature(const char *username, const char *password, 
                           time_t timestamp, const char *received_sig)
{
    char input[512];
    char *calculated_sig;
    int result = 0;
    
    snprintf(input, sizeof(input), "%s%s%ld%s", 
             username, password, timestamp, SIGN_KEY);
    
    printf("Signature input: %s\n", input);
    
    calculated_sig = calculate_md5(input);
    if (calculated_sig) {
        printf("Calculated signature: %s\n", calculated_sig);
        printf("Received signature:   %s\n", received_sig);
        result = (strcmp(calculated_sig, received_sig) == 0);
        free(calculated_sig);
    }
    
    return result;
}

int test_authentication(const char *username, const char *password)
{
    CURL *curl;
    CURLcode res;
    struct http_response response = {0};
    char post_data[512];
    json_object *json_resp, *pass_obj, *t_obj, *s_obj;
    int pass_value = 0;
    time_t timestamp = 0;
    const char *signature = NULL;
    int auth_result = 0;
    
    printf("Testing authentication for user: %s\n", username);
    printf("URL: %s\n", AUTH_URL);
    
    curl = curl_easy_init();
    if (!curl) {
        printf("Failed to initialize curl\n");
        return 0;
    }
    
    /* Prepare POST data */
    snprintf(post_data, sizeof(post_data), "u=%s&p=%s", username, password);
    printf("POST data: %s\n", post_data);
    
    /* Set curl options */
    curl_easy_setopt(curl, CURLOPT_URL, AUTH_URL);
    curl_easy_setopt(curl, CURLOPT_POSTFIELDS, post_data);
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, write_callback);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 30L);
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);
    
    /* Perform the request */
    res = curl_easy_perform(curl);
    curl_easy_cleanup(curl);
    
    if (res != CURLE_OK) {
        printf("Request failed: %s\n", curl_easy_strerror(res));
        if (response.data) free(response.data);
        return 0;
    }
    
    if (!response.data) {
        printf("Empty response from server\n");
        return 0;
    }
    
    printf("Server response: %s\n", response.data);
    
    /* Parse JSON response */
    json_resp = json_tokener_parse(response.data);
    if (!json_resp) {
        printf("Invalid JSON response\n");
        free(response.data);
        return 0;
    }
    
    /* Extract values from JSON */
    if (json_object_object_get_ex(json_resp, "pass", &pass_obj)) {
        pass_value = json_object_get_int(pass_obj);
        printf("Pass value: %d\n", pass_value);
    }
    
    if (json_object_object_get_ex(json_resp, "t", &t_obj)) {
        timestamp = json_object_get_int64(t_obj);
        printf("Timestamp: %ld\n", timestamp);
    }
    
    if (json_object_object_get_ex(json_resp, "s", &s_obj)) {
        signature = json_object_get_string(s_obj);
        printf("Signature: %s\n", signature);
    }
    
    /* Verify signature */
    if (signature && verify_signature(username, password, timestamp, signature)) {
        printf("Signature verification: PASSED\n");
        if (pass_value == 1) {
            auth_result = 1;
            printf("Authentication result: SUCCESS\n");
        } else {
            printf("Authentication result: FAILED (pass=%d)\n", pass_value);
        }
    } else {
        printf("Signature verification: FAILED\n");
    }
    
    /* Cleanup */
    json_object_put(json_resp);
    free(response.data);
    
    return auth_result;
}

int main(int argc, char *argv[])
{
    const char *username = "testuser";
    const char *password = "testpass";
    
    if (argc >= 3) {
        username = argv[1];
        password = argv[2];
    } else if (argc == 2) {
        printf("Usage: %s <username> <password>\n", argv[0]);
        printf("Using default credentials for testing...\n");
    }
    
    curl_global_init(CURL_GLOBAL_DEFAULT);
    
    printf("=== HTTP Authentication Test ===\n");
    int result = test_authentication(username, password);
    
    printf("\n=== Test Result ===\n");
    printf("Authentication %s\n", result ? "SUCCESSFUL" : "FAILED");
    
    curl_global_cleanup();
    
    return result ? 0 : 1;
}
