# Makefile for HTTP Authentication plugin
# Optimized for Ubuntu 24.04
# Copyright 2024

# Compiler settings
CC ?= gcc
COPTS ?= -O2

# System paths for Ubuntu 24.04
DESTDIR ?=
MANDIR = $(DESTDIR)/usr/share/man/man8
PPPD_VERSION ?= $(shell pppd --version 2>&1 | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+' | head -1 || echo "2.4.9")
LIBDIR = $(DESTDIR)/usr/lib/pppd/$(PPPD_VERSION)

INSTALL = install

PLUGIN = httpauth.so

# Include paths for Ubuntu 24.04
CFLAGS = $(COPTS) -I/usr/include/pppd -fPIC -Wall -Wextra -std=c99
CFLAGS += $(shell pkg-config --cflags libcurl json-c openssl)
CFLAGS += -Wno-unused-const-variable -Wno-missing-field-initializers

LDFLAGS = -shared

# Required libraries with pkg-config
LIBS = $(shell pkg-config --libs libcurl json-c openssl)

all: $(PLUGIN)

install: all
	@echo "Installing to $(LIBDIR)"
	$(INSTALL) -d -m 755 $(LIBDIR)
	$(INSTALL) -c -m 755 httpauth.so $(LIBDIR)
	@echo "Installing man page to $(MANDIR)"
	$(INSTALL) -d -m 755 $(MANDIR)
	$(INSTALL) -c -m 644 httpauth.8 $(MANDIR) || true
	@echo "Installation completed"
	@echo "Plugin installed: $(LIBDIR)/httpauth.so"

httpauth.so: httpauth.o
	@echo "Linking plugin..."
	$(CC) $(LDFLAGS) -o httpauth.so httpauth.o $(LIBS)

httpauth.o: httpauth.c httpauth.h
	@echo "Compiling httpauth.c..."
	$(CC) $(CFLAGS) -c httpauth.c

# Test program
test: test_auth

test_auth: test_auth.c
	@echo "Building test program..."
	$(CC) -o test_auth test_auth.c $(shell pkg-config --cflags --libs libcurl json-c openssl)

clean:
	rm -f *.o *.so test_auth

distclean: clean

dist-clean: distclean

# Show configuration
config:
	@echo "Configuration:"
	@echo "  CC: $(CC)"
	@echo "  CFLAGS: $(CFLAGS)"
	@echo "  LIBS: $(LIBS)"
	@echo "  PPPD_VERSION: $(PPPD_VERSION)"
	@echo "  LIBDIR: $(LIBDIR)"
	@echo "  MANDIR: $(MANDIR)"

.PHONY: all install clean distclean dist-clean test config
