# HTTP认证插件 - PPP/L2TP (RADIUS风格IP授权版)

这是一个用于PPP守护进程的HTTP认证插件，专为xl2tpd L2TP VPN服务器设计。插件通过HTTP API进行用户认证，集成了RADIUS风格的IP授权机制，彻底解决IP授权问题。

## 核心特性

- **HTTP API认证** - 通过HTTP POST请求进行用户验证
- **MD5签名验证** - 确保响应完整性和安全性
- **RADIUS风格IP授权** - 集成完整的IP地址授权机制
- **精确计费系统** - 基于时间和流量的完整计费功能
- **完整限速功能** - 会话/空闲超时、流量限制、MTU控制
- **会话管理** - 开始/中间/停止计费，唯一会话ID跟踪
- **自动IP地址管理** - 支持IP范围 **********0-**********00
- **xl2tpd完美集成** - 专为xl2tpd L2TP服务器优化
- **简洁参数格式** - 使用u/p参数减少网络传输
- **Ubuntu 24.04兼容** - 完全兼容最新Ubuntu LTS版本
- **实时统计** - 精确的字节数和包数统计

## 系统要求

- Ubuntu 24.04 LTS
- pppd 2.4.9+
- xl2tpd
- libcurl4-openssl-dev
- libjson-c-dev
- libssl-dev

## 快速安装

### 标准版本 (认证 + IP授权)
```bash
cd final-plugin
sudo ./install.sh
```

### 完整版本 (认证 + IP授权 + 计费系统)
```bash
cd final-plugin
sudo ./install-with-accounting.sh
```

**完整版安装脚本功能：**
1. ✅ 安装所有依赖包
2. ✅ 编译集成RADIUS风格IP授权和计费的插件
3. ✅ 创建优化的配置文件
4. ✅ 配置计费系统参数
5. ✅ 自动启动和启用xl2tpd服务
6. ✅ 安装认证和计费测试程序
7. ✅ 验证安装和配置
8. ✅ 显示详细的使用说明

## 快速测试

安装完成后，运行测试：
```bash
# 测试HTTP认证
test_auth test001 password

# 测试计费系统 (完整版)
test_accounting test001
```

## RADIUS风格IP授权机制

### 问题解决
传统的HTTP认证插件只处理用户认证，不处理IP授权，导致：
- ✅ HTTP认证成功
- ❌ IP授权失败：`Peer is not authorized to use remote address`

### 解决方案
参考RADIUS插件实现，集成了完整的IP授权机制：

#### 1. 认证状态管理
```c
struct httpauth_state {
    int any_ip_addr_ok;      /* 允许任何IP */
    u_int32_t ip_addr;       /* 指定IP地址 */
    char user[MAXNAMELEN];   /* 用户名 */
    int session_timeout;     /* 会话超时 */
    int idle_timeout;        /* 空闲超时 */
};
```

#### 2. 钩子函数注册
```c
allowed_address_hook = httpauth_allowed_address;
ip_choose_hook = httpauth_choose_ip;
```

#### 3. IP授权检查
```c
static int httpauth_allowed_address(u_int32_t addr) {
    if (hstate.any_ip_addr_ok) {
        return 1;  /* 授权通过 */
    }
    return 0;
}
```

### 工作流程
1. **用户认证** → HTTP API验证用户身份
2. **设置授权策略** → `any_ip_addr_ok = 1`
3. **IP协商** → pppd调用 `httpauth_allowed_address()`
4. **授权通过** → 客户端获得IP地址
5. **连接成功** → 不再出现IP授权错误

## 计费系统 (完整版)

### 计费功能
- **会话开始计费** - 连接建立时自动发送
- **中间计费** - 定期发送当前统计 (默认5分钟)
- **会话停止计费** - 连接断开时自动发送
- **时间统计** - 精确到秒的会话时间
- **流量统计** - 上传/下载字节数和包数

### 计费配置
```
# 计费系统配置
acct-url https://testapi.softapi.cn/notify/pcm_acct
do-accounting true
acct-interim-interval 300
```

### 计费API格式
```
POST https://testapi.softapi.cn/notify/pcm_acct

acct_type=Start&u=用户名&session_id=会话ID&session_time=会话时间&
bytes_in=输入字节&bytes_out=输出字节&pkts_in=输入包数&pkts_out=输出包数&
t=时间戳&s=MD5签名
```

### 计费类型
- `Start` - 会话开始，流量为0
- `Interim-Update` - 中间更新，包含当前统计
- `Stop` - 会话结束，包含总统计

详细说明请参考 [ACCOUNTING-SYSTEM.md](ACCOUNTING-SYSTEM.md)

## 限速系统 (完整版)

### 限速功能
- **会话超时** - 限制连接的最大持续时间
- **空闲超时** - 在无活动时自动断开连接
- **流量限制** - 限制用户的总流量使用
- **带宽限制** - 精确控制上传/下载速度
- **MTU控制** - 限制最大传输单元大小
- **过滤器支持** - 通过脚本实现高级流量控制

### 限速配置
```
# 限速系统配置
default-session-timeout 3600
default-idle-timeout 600
default-max-octets **********
default-max-octets-dir 0
default-framed-mtu 1400
```

### 认证响应中的限速参数
```json
{
    "pass": 1,
    "session_timeout": 3600,
    "idle_timeout": 600,
    "max_octets": **********,
    "max_octets_dir": 0,
    "framed_mtu": 1400,
    "filter_id": "premium"
}
```

### 带宽限制套餐
- **basic**: 2Mbps下载 / 512Kbps上传
- **premium**: 10Mbps下载 / 2Mbps上传
- **enterprise**: 50Mbps下载 / 10Mbps上传
- **unlimited**: 不限速

### 测试带宽限制
```bash
# 测试带宽限制配置
sudo ./test_bandwidth.sh

# 实时监控带宽使用
sudo tc -s class show dev ppp0
```

详细说明请参考：
- [RATE-LIMITING.md](RATE-LIMITING.md) - 完整限速功能
- [BANDWIDTH-LIMITING.md](BANDWIDTH-LIMITING.md) - 带宽限制详解

## 手动安装

```bash
# 安装依赖
sudo apt-get update
sudo apt-get install ppp-dev libcurl4-openssl-dev libjson-c-dev libssl-dev build-essential pkg-config xl2tpd

# 编译插件
make clean
make

# 安装插件
sudo make install

# 构建测试程序
make test
sudo cp test_auth /usr/local/bin/
```

## 配置

### PPP配置 (/etc/ppp/options.xl2tpd)

```
# 认证设置
require-pap
refuse-chap
refuse-mschap
refuse-mschap-v2
refuse-eap

# HTTP认证插件
plugin /usr/lib/pppd/2.4.9/httpauth.so
auth-url https://testapi.softapi.cn/notify/pcm_ok
sign-key hYC0ztcOKp2aZ5t0

# IP授权设置
noauth
ipcp-accept-local
ipcp-accept-remote

# 网络设置
proxyarp
nobsdcomp
nodeflate
noipdefault

# DNS设置
ms-dns *******
ms-dns *******

# 连接设置
lcp-echo-interval 30
lcp-echo-failure 4
idle 1800
logfile /var/log/xl2tpd.log
```

### xl2tpd配置 (/etc/xl2tpd/xl2tpd.conf)

```ini
[global]
listen-addr = 0.0.0.0
port = 1701
auth file = /etc/xl2tpd/l2tp-secrets

[lns default]
name = HTTPAuthVPNServer
ip range = **********0-**********00
local ip = **********
require chap = no
refuse pap = no
require authentication = yes
pppoptfile = /etc/ppp/options.xl2tpd
length bit = yes
challenge = no
exclusive = no
```

## API接口

### 请求格式

```
POST https://testapi.softapi.cn/notify/pcm_ok
Content-Type: application/x-www-form-urlencoded

u=用户名&p=密码
```

### 响应格式

```json
{
    "pass": 1,
    "t": 1752454664,
    "s": "673261d844d929b52a468404aac290ca"
}
```

### 字段说明

- `pass`: 认证结果 (1=成功, 其他=失败)
- `t`: 服务器时间戳
- `s`: MD5签名 = md5(用户名 + 密码 + 时间戳 + 签名密钥)

## 服务器端实现

### PHP示例

```php
<?php
header('Content-Type: application/json');

$username = $_POST['u'] ?? '';
$password = $_POST['p'] ?? '';
$sign_key = 'hYC0ztcOKp2aZ5t0';

// 你的认证逻辑
$authenticated = authenticate_user($username, $password);

$timestamp = time();
$signature = md5($username . $password . $timestamp . $sign_key);

echo json_encode([
    'pass' => $authenticated ? 1 : 0,
    't' => $timestamp,
    's' => $signature
]);
?>
```

### Python Flask示例

```python
from flask import Flask, request, jsonify
import hashlib
import time

app = Flask(__name__)
SIGN_KEY = 'hYC0ztcOKp2aZ5t0'

@app.route('/notify/pcm_ok', methods=['POST'])
def authenticate():
    username = request.form.get('u', '')
    password = request.form.get('p', '')
    
    # 你的认证逻辑
    authenticated = check_user(username, password)
    
    timestamp = int(time.time())
    signature_input = f"{username}{password}{timestamp}{SIGN_KEY}"
    signature = hashlib.md5(signature_input.encode()).hexdigest()
    
    return jsonify({
        'pass': 1 if authenticated else 0,
        't': timestamp,
        's': signature
    })
```

## 使用方法

### 1. 启动xl2tpd服务

```bash
# 启动服务
sudo systemctl start xl2tpd

# 开机自启
sudo systemctl enable xl2tpd

# 查看状态
sudo systemctl status xl2tpd
```

### 2. 测试认证

```bash
# 测试HTTP认证API
test_auth 用户名 密码

# 测试插件加载
sudo pppd plugin /usr/lib/pppd/2.4.9/httpauth.so nodetach debug
```

### 3. 客户端连接

客户端配置L2TP VPN连接：
- 服务器地址：你的服务器IP
- 用户名：通过HTTP API验证的用户名
- 密码：对应的密码
- 协议：L2TP (可选择IPSec)

## 故障排除

### 1. 查看日志

```bash
# xl2tpd日志
sudo tail -f /var/log/xl2tpd.log

# 系统日志
sudo journalctl -u xl2tpd -f

# PPP调试日志
sudo tail -f /var/log/ppp.log
```

### 2. 常见问题

**插件加载失败**
```bash
# 检查插件是否存在
ls -la /usr/lib/pppd/*/httpauth.so

# 检查依赖
ldd /usr/lib/pppd/2.4.9/httpauth.so
```

**认证失败**
```bash
# 测试API连接
curl -X POST https://testapi.softapi.cn/notify/pcm_ok -d "u=test&p=test"

# 检查签名计算
test_auth 用户名 密码
```

**IP授权错误**
```bash
# 检查PPP配置中是否包含
# noauth
# ipcp-accept-local
# ipcp-accept-remote
```

### 3. 调试模式

在`/etc/ppp/options.xl2tpd`中添加：
```
debug
logfile /var/log/ppp-debug.log
```

## 安全注意事项

1. **使用HTTPS** - 生产环境必须使用HTTPS
2. **保护签名密钥** - sign-key应该保密并定期更换
3. **防火墙配置** - 限制L2TP端口(1701)的访问
4. **日志监控** - 监控认证失败和异常连接
5. **IPSec加密** - 考虑启用IPSec提供额外安全层

## 文件说明

- `httpauth.c` - 插件源码（中文注释）
- `Makefile` - 编译配置
- `test_auth.c` - 测试程序（中文注释）
- `install.sh` - 安装脚本（中文界面）
- `README.md` - 使用说明（中文版）

## 技术支持

- 查看日志文件排查问题
- 使用test_auth程序测试API
- 检查网络连接和防火墙设置
- 验证服务器端API实现

## 许可证

本插件基于GNU General Public License v2发布。
